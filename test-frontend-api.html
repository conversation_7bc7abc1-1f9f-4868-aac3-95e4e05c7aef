<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Frontend API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .log { max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Frontend API Test</h1>
        
        <div class="section info">
            <h3>Test Authentication and Bookings API</h3>
            <p>This page tests the frontend API calls to debug the 403 error.</p>
        </div>

        <div class="section">
            <h3>1. Login Test</h3>
            <button onclick="testLogin()">Test Login</button>
            <div id="loginResult"></div>
        </div>

        <div class="section">
            <h3>2. Token Check</h3>
            <button onclick="checkToken()">Check Stored Token</button>
            <div id="tokenResult"></div>
        </div>

        <div class="section">
            <h3>3. Bookings API Test</h3>
            <button onclick="testBookingsAPI()">Test Bookings API</button>
            <div id="bookingsResult"></div>
        </div>

        <div class="section">
            <h3>4. Debug Logs</h3>
            <button onclick="clearLogs()">Clear Logs</button>
            <div id="debugLogs" class="log"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3000/api';
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('debugLogs');
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logEntry.className = type;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function clearLogs() {
            document.getElementById('debugLogs').innerHTML = '';
        }

        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.innerHTML = '<p>Testing login...</p>';
            
            try {
                log('🔍 Testing login with credentials...', 'info');
                
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123456'
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    log('✅ Login successful', 'success');
                    log(`User: ${data.data.user.email} (${data.data.user.role})`, 'info');
                    log(`Token: ${data.data.token.substring(0, 30)}...`, 'info');
                    
                    // Store token in localStorage
                    localStorage.setItem('accessToken', data.data.token);
                    localStorage.setItem('refreshToken', data.data.refreshToken);
                    localStorage.setItem('user', JSON.stringify(data.data.user));
                    
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Login Successful</h4>
                            <p><strong>User:</strong> ${data.data.user.email}</p>
                            <p><strong>Role:</strong> ${data.data.user.role}</p>
                            <p><strong>Token:</strong> ${data.data.token.substring(0, 50)}...</p>
                        </div>
                    `;
                } else {
                    log(`❌ Login failed: ${data.message}`, 'error');
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Login Failed</h4>
                            <p>${data.message}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                log(`❌ Login error: ${error.message}`, 'error');
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Login Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        function checkToken() {
            const resultDiv = document.getElementById('tokenResult');
            
            const token = localStorage.getItem('accessToken');
            const user = localStorage.getItem('user');
            
            if (token) {
                try {
                    // Decode JWT payload
                    const payload = JSON.parse(atob(token.split('.')[1]));
                    const currentTime = Math.floor(Date.now() / 1000);
                    const isExpired = payload.exp < currentTime;
                    
                    log(`🔍 Token found: ${token.substring(0, 30)}...`, 'info');
                    log(`Token expires: ${new Date(payload.exp * 1000).toLocaleString()}`, 'info');
                    log(`Is expired: ${isExpired}`, isExpired ? 'error' : 'success');
                    
                    resultDiv.innerHTML = `
                        <div class="${isExpired ? 'error' : 'success'}">
                            <h4>🔑 Token Status</h4>
                            <p><strong>Token:</strong> ${token.substring(0, 50)}...</p>
                            <p><strong>User:</strong> ${user}</p>
                            <p><strong>Expires:</strong> ${new Date(payload.exp * 1000).toLocaleString()}</p>
                            <p><strong>Status:</strong> ${isExpired ? 'EXPIRED' : 'VALID'}</p>
                            <pre>${JSON.stringify(payload, null, 2)}</pre>
                        </div>
                    `;
                } catch (error) {
                    log(`❌ Token decode error: ${error.message}`, 'error');
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Invalid Token</h4>
                            <p>Could not decode token: ${error.message}</p>
                        </div>
                    `;
                }
            } else {
                log('❌ No token found in localStorage', 'error');
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ No Token</h4>
                        <p>No access token found in localStorage. Please login first.</p>
                    </div>
                `;
            }
        }

        async function testBookingsAPI() {
            const resultDiv = document.getElementById('bookingsResult');
            resultDiv.innerHTML = '<p>Testing bookings API...</p>';
            
            const token = localStorage.getItem('accessToken');
            
            if (!token) {
                log('❌ No token available for bookings test', 'error');
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ No Token</h4>
                        <p>Please login first to test bookings API.</p>
                    </div>
                `;
                return;
            }
            
            try {
                log('🔍 Testing bookings API with token...', 'info');
                log(`Authorization: Bearer ${token.substring(0, 30)}...`, 'info');
                
                const response = await fetch(`${API_BASE_URL}/bookings`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    log(`✅ Bookings API successful: ${data.data.length} bookings`, 'success');
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Bookings API Success</h4>
                            <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                            <p><strong>Bookings Count:</strong> ${data.data.length}</p>
                            <p><strong>Message:</strong> ${data.message}</p>
                            <pre>${JSON.stringify(data, null, 2).substring(0, 1000)}...</pre>
                        </div>
                    `;
                } else {
                    log(`❌ Bookings API failed: ${response.status} ${data.message}`, 'error');
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Bookings API Failed</h4>
                            <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                            <p><strong>Error:</strong> ${data.message}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                log(`❌ Bookings API error: ${error.message}`, 'error');
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Bookings API Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // Auto-check token on page load
        window.onload = function() {
            log('🚀 Page loaded, checking existing token...', 'info');
            checkToken();
        };
    </script>
</body>
</html>
