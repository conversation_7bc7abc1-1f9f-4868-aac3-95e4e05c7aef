/**
 * Auth Model
 * User authentication and session management
 */

const { DataTypes } = require('sequelize');
const { sequelize } = require('../../database/connection');
const bcrypt = require('bcryptjs');
const config = require('../../config/environment');

const User = sequelize.define('User', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  email: {
    type: DataTypes.STRING(100),
    unique: true,
    allowNull: false,
    validate: {
      isEmail: true,
      len: [5, 100]
    },
    comment: 'User email address'
  },
  password: {
    type: DataTypes.STRING(255),
    allowNull: false,
    validate: {
      len: [6, 255]
    },
    comment: 'Hashed password'
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    validate: {
      len: [2, 100],
      notEmpty: true
    },
    comment: 'Full name'
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
    validate: {
      is: /^(0|\+84)[0-9]{9,10}$/
    },
    comment: 'Phone number (Vietnamese format)'
  },
  role: {
    type: DataTypes.ENUM('admin', 'staff', 'customer'),
    defaultValue: 'customer',
    allowNull: false,
    comment: 'User role'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active',
    comment: 'Account status'
  },
  isEmailVerified: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'is_email_verified',
    comment: 'Email verification status'
  },
  emailVerificationToken: {
    type: DataTypes.STRING(255),
    allowNull: true,
    field: 'email_verification_token',
    comment: 'Email verification token'
  },
  emailVerificationExpires: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'email_verification_expires',
    comment: 'Email verification token expiry'
  },
  passwordResetToken: {
    type: DataTypes.STRING(255),
    allowNull: true,
    field: 'password_reset_token',
    comment: 'Password reset token'
  },
  passwordResetExpires: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'password_reset_expires',
    comment: 'Password reset token expiry'
  },
  lastLogin: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'last_login',
    comment: 'Last login timestamp'
  },
  loginAttempts: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'login_attempts',
    comment: 'Failed login attempts count'
  },
  lockUntil: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'lock_until',
    comment: 'Account lock expiry time'
  },
  dateOfBirth: {
    type: DataTypes.DATEONLY,
    allowNull: true,
    field: 'date_of_birth',
    comment: 'Date of birth'
  },
  gender: {
    type: DataTypes.ENUM('male', 'female', 'other'),
    allowNull: true,
    comment: 'Gender'
  },
  avatar: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Avatar image URL'
  },
  branchId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'branch_id',
    references: {
      model: 'branches',
      key: 'id'
    },
    comment: 'Associated branch (for staff)'
  }
}, {
  tableName: 'users',
  timestamps: true,
  underscored: true,
  paranoid: true, // Soft delete
  
  // Indexes
  indexes: [
    { fields: ['email'], unique: true },
    { fields: ['phone'] },
    { fields: ['role'] },
    { fields: ['is_active'] },
    { fields: ['branch_id'] },
    { fields: ['created_at'] },
    { fields: ['email_verification_token'] },
    { fields: ['password_reset_token'] }
  ],
  
  // Hooks
  hooks: {
    beforeCreate: async (user) => {
      if (user.password) {
        user.password = await bcrypt.hash(user.password, config.security.bcryptRounds);
      }
    },
    beforeUpdate: async (user) => {
      if (user.changed('password')) {
        user.password = await bcrypt.hash(user.password, config.security.bcryptRounds);
      }
    },
    beforeBulkCreate: async (users) => {
      for (const user of users) {
        if (user.password) {
          user.password = await bcrypt.hash(user.password, config.security.bcryptRounds);
        }
      }
    }
  },
  
  // Scopes
  scopes: {
    active: {
      where: { isActive: true }
    },
    withoutPassword: {
      attributes: { exclude: ['password'] }
    },
    withoutSensitive: {
      attributes: { 
        exclude: [
          'password', 
          'emailVerificationToken', 
          'passwordResetToken',
          'loginAttempts',
          'lockUntil'
        ] 
      }
    },
    byRole: (role) => ({
      where: { role }
    })
  }
});

// Define associations
User.associate = (models) => {
  // User belongs to Branch (for staff members)
  User.belongsTo(models.branches, {
    foreignKey: 'branch_id',
    as: 'branch',
    allowNull: true
  });

  // User has many Bookings (as customer)
  User.hasMany(models.bookings, {
    foreignKey: 'customer_id',
    as: 'bookings'
  });

  // User has many Bookings (as assigned staff)
  User.hasMany(models.bookings, {
    foreignKey: 'employee_id',
    as: 'assignedBookings'
  });
};

// Instance methods
User.prototype.comparePassword = async function(password) {
  return await bcrypt.compare(password, this.password);
};

User.prototype.toJSON = function() {
  const values = { ...this.get() };
  delete values.password;
  delete values.emailVerificationToken;
  delete values.passwordResetToken;
  delete values.loginAttempts;
  delete values.lockUntil;
  return values;
};

User.prototype.isLocked = function() {
  return !!(this.lockUntil && this.lockUntil > Date.now());
};

User.prototype.incLoginAttempts = async function() {
  // If we have a previous lock that has expired, restart at 1
  if (this.lockUntil && this.lockUntil < Date.now()) {
    return this.update({
      loginAttempts: 1,
      lockUntil: null
    });
  }
  
  const updates = { loginAttempts: this.loginAttempts + 1 };
  
  // Lock account after max attempts
  const maxAttempts = 5;
  const lockTime = 30 * 60 * 1000; // 30 minutes
  
  if (this.loginAttempts + 1 >= maxAttempts && !this.isLocked()) {
    updates.lockUntil = Date.now() + lockTime;
  }
  
  return this.update(updates);
};

User.prototype.resetLoginAttempts = async function() {
  return this.update({
    loginAttempts: 0,
    lockUntil: null
  });
};

// Class methods
User.findByEmail = function(email) {
  return this.findOne({ where: { email: email.toLowerCase() } });
};

User.findByEmailVerificationToken = function(token) {
  return this.findOne({ 
    where: { 
      emailVerificationToken: token,
      emailVerificationExpires: { [sequelize.Sequelize.Op.gt]: new Date() }
    } 
  });
};

User.findByPasswordResetToken = function(token) {
  return this.findOne({ 
    where: { 
      passwordResetToken: token,
      passwordResetExpires: { [sequelize.Sequelize.Op.gt]: new Date() }
    } 
  });
};

// Duplicate associate function removed - using the one above

module.exports = User;
