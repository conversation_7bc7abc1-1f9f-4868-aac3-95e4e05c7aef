<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Demo trang đặt lịch hẹn trực tuyến - FormBooker">
    <meta name="keywords" content="đặt lịch, booking, appointment, online booking">
    <meta name="author" content="FormBooker">
    <title>Demo Booking Form - FormBooker</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📅</text></svg>">
    
    <!-- CSS Styling -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, Oxygen, <PERSON>bu<PERSON>u, Can<PERSON>ell, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .badge {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            backdrop-filter: blur(10px);
        }

        .content {
            padding: 40px;
        }

        .iframe-container {
            position: relative;
            width: 100%;
            margin: 0 auto;
            background: #f8fafc;
            border-radius: 12px;
            padding: 20px;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
        }

        .iframe-wrapper {
            position: relative;
            width: 100%;
            height: 600px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .booking-iframe {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 8px;
            transition: opacity 0.3s ease;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 10;
            transition: opacity 0.3s ease;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e5e7eb;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            display: none;
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin-top: 20px;
        }

        .error-message h3 {
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        .error-message p {
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .retry-button {
            background: #dc2626;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: background 0.2s ease;
        }

        .retry-button:hover {
            background: #b91c1c;
        }

        .info-section {
            margin-top: 30px;
            padding: 20px;
            background: #f0f9ff;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }

        .info-section h3 {
            color: #1e40af;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .info-section ul {
            list-style: none;
            padding-left: 0;
        }

        .info-section li {
            padding: 5px 0;
            color: #1e40af;
        }

        .info-section li:before {
            content: "✓ ";
            color: #10b981;
            font-weight: bold;
            margin-right: 8px;
        }

        .footer {
            background: #f8fafc;
            padding: 20px;
            text-align: center;
            color: #6b7280;
            font-size: 0.9rem;
            border-top: 1px solid #e5e7eb;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .content {
                padding: 20px;
            }

            .iframe-container {
                padding: 15px;
            }

            .iframe-wrapper {
                height: 500px;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.8rem;
            }

            .iframe-wrapper {
                height: 450px;
            }
        }

        /* Print styles */
        @media print {
            body {
                background: white;
            }
            
            .container {
                box-shadow: none;
            }
            
            .iframe-container {
                display: none;
            }
            
            .info-section:after {
                content: "Vui lòng truy cập website để đặt lịch hẹn trực tuyến.";
                display: block;
                margin-top: 20px;
                font-weight: bold;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            <h1>📅 Demo Booking Form</h1>
            <p>Trải nghiệm hệ thống đặt lịch hẹn trực tuyến của FormBooker</p>
            <span class="badge">🚀 Live Demo</span>
        </div>

        <!-- Main Content -->
        <div class="content">
            <!-- Iframe Container -->
            <div class="iframe-container">
                <div class="iframe-wrapper">
                    <!-- Loading Overlay -->
                    <div class="loading-overlay" id="loadingOverlay">
                        <div class="loading-spinner"></div>
                        <p>Đang tải form đặt lịch...</p>
                    </div>

                    <!-- Booking Form Iframe -->
                    <iframe 
                        id="bookingIframe"
                        class="booking-iframe"
                        src="http://localhost:4000/book/cng-ty-tnhh" 
                        width="100%" 
                        height="600" 
                        frameborder="0" 
                        style="border: none; border-radius: 8px;" 
                        allowtransparency="true" 
                        loading="lazy"
                        title="Booking Form - Đặt lịch hẹn trực tuyến">
                    </iframe>
                </div>

                <!-- Error Message -->
                <div class="error-message" id="errorMessage">
                    <h3>⚠️ Không thể tải form đặt lịch</h3>
                    <p>Có vẻ như server đang bảo trì hoặc có vấn đề kết nối. Vui lòng thử lại sau ít phút.</p>
                    <button class="retry-button" onclick="retryLoadIframe()">🔄 Thử lại</button>
                </div>
            </div>

        <!-- Footer -->
        <div class="footer">
            <p>© 2025 FormBooker - Hệ thống đặt lịch hẹn trực tuyến | 
            <strong>Demo Page</strong> | 
            Powered by <a href="#" style="color: #3b82f6; text-decoration: none;">FormBooker</a></p>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Iframe loading management
        const iframe = document.getElementById('bookingIframe');
        const loadingOverlay = document.getElementById('loadingOverlay');
        const errorMessage = document.getElementById('errorMessage');
        let loadTimeout;

        // Show loading overlay initially
        loadingOverlay.style.display = 'flex';

        // Handle iframe load success
        iframe.addEventListener('load', function() {
            console.log('✅ Booking form loaded successfully');
            hideLoading();
            clearTimeout(loadTimeout);
        });

        // Handle iframe load error
        iframe.addEventListener('error', function() {
            console.error('❌ Failed to load booking form');
            showError();
            clearTimeout(loadTimeout);
        });

        // Set timeout for loading (30 seconds)
        loadTimeout = setTimeout(function() {
            console.warn('⏰ Iframe loading timeout');
            showError();
        }, 30000);

        // Hide loading overlay
        function hideLoading() {
            loadingOverlay.style.opacity = '0';
            setTimeout(function() {
                loadingOverlay.style.display = 'none';
            }, 300);
        }

        // Show error message
        function showError() {
            hideLoading();
            errorMessage.style.display = 'block';
        }

        // Retry loading iframe
        function retryLoadIframe() {
            console.log('🔄 Retrying iframe load...');
            errorMessage.style.display = 'none';
            loadingOverlay.style.display = 'flex';
            loadingOverlay.style.opacity = '1';
            
            // Reload iframe
            const currentSrc = iframe.src;
            iframe.src = '';
            setTimeout(function() {
                iframe.src = currentSrc;
            }, 100);

            // Reset timeout
            clearTimeout(loadTimeout);
            loadTimeout = setTimeout(function() {
                showError();
            }, 30000);
        }


        // Track iframe interactions (optional analytics)
        iframe.addEventListener('load', function() {
            // You can add analytics tracking here
            console.log('📊 Analytics: Iframe loaded successfully');
        });

        // Handle window resize for responsive iframe
        window.addEventListener('resize', function() {
            // Iframe automatically adjusts due to CSS, but you can add custom logic here
            console.log('📱 Window resized, iframe responsive adjustment');
        });

        // Prevent right-click on iframe (optional security)
        iframe.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            console.log('🔒 Right-click disabled on iframe');
        });

        // Add keyboard shortcuts for demo
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + R to reload iframe
            if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
                e.preventDefault();
                retryLoadIframe();
                console.log('⌨️ Keyboard shortcut: Iframe reloaded');
            }
        });
    </script>
</body>
</html>
