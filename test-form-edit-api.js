const axios = require('axios');

async function testFormEditAPI() {
  console.log('🧪 Testing form edit API endpoint...');
  
  const API_BASE = 'http://localhost:3000/api';
  
  try {
    // 1. Login to get token
    console.log('\n1. Logging in...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123456'
    });
    
    const token = loginResponse.data.data.token;
    console.log('✅ Login successful');
    
    // 2. Get list of forms to find a valid form ID
    console.log('\n2. Getting forms list...');
    const formsResponse = await axios.get(`${API_BASE}/forms`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    const forms = formsResponse.data.data.forms;
    console.log(`✅ Found ${forms.length} forms`);
    
    if (forms.length === 0) {
      console.log('❌ No forms found. Please create a form first.');
      return;
    }
    
    const testForm = forms[0];
    console.log(`📝 Using form: ${testForm.name} (ID: ${testForm.id})`);
    
    // 3. Test getting form by ID (for edit)
    console.log('\n3. Testing GET /api/forms/:id...');
    const formResponse = await axios.get(`${API_BASE}/forms/${testForm.id}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log('✅ Form retrieved successfully');
    console.log('Form data:', JSON.stringify({
      id: formResponse.data.data.id,
      name: formResponse.data.data.name,
      serviceId: formResponse.data.data.serviceId,
      branchId: formResponse.data.data.branchId,
      status: formResponse.data.data.status,
      fieldsConfig: formResponse.data.data.fieldsConfig,
      brandingConfig: formResponse.data.data.brandingConfig
    }, null, 2));
    
    // 4. Test updating form
    console.log('\n4. Testing PUT /api/forms/:id...');
    const updateData = {
      name: formResponse.data.data.name + ' (Updated)',
      serviceId: formResponse.data.data.serviceId,
      branchId: formResponse.data.data.branchId,
      status: formResponse.data.data.status,
      fieldsConfig: formResponse.data.data.fieldsConfig,
      brandingConfig: {
        ...formResponse.data.data.brandingConfig,
        primaryColor: '#ff6b6b'
      }
    };
    
    const updateResponse = await axios.put(`${API_BASE}/forms/${testForm.id}`, updateData, {
      headers: { 
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Form updated successfully');
    console.log('Updated form name:', updateResponse.data.data.name);
    console.log('Updated primary color:', updateResponse.data.data.brandingConfig?.primaryColor);
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
    
    if (error.request) {
      console.error('Request details:', {
        method: error.config?.method,
        url: error.config?.url,
        headers: error.config?.headers
      });
    }
    
    console.error('Full error stack:', error.stack);
  }
}

// Run the test
testFormEditAPI().catch(console.error);
