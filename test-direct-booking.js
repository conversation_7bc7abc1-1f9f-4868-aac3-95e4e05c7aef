const axios = require('axios');

async function testDirectBooking() {
  console.log('🧪 Testing direct public booking endpoint...');
  
  const API_BASE = 'http://localhost:3000/api';
  
  try {
    // Test with the form slug we found earlier
    const formSlug = 'enhanced-features-test-1750064249116';
    
    console.log(`📝 Testing booking with form slug: ${formSlug}`);
    
    const bookingData = {
      formSlug: formSlug,
      customerName: 'Test Customer Direct',
      phoneNumber: '0987654321',
      emailAddress: '<EMAIL>',
      preferredDate: '2025-06-25',
      preferredTime: '14:00',
      specialRequests: 'Test booking from direct script'
    };
    
    console.log('📝 Booking data:', JSON.stringify(bookingData, null, 2));
    
    const response = await axios.post(`${API_BASE}/public/bookings`, bookingData, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 30000 // 30 second timeout
    });
    
    console.log('✅ Booking created successfully!');
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Status text:', error.response.statusText);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
      console.error('Response headers:', error.response.headers);
    }
    
    if (error.request) {
      console.error('Request details:', {
        method: error.config?.method,
        url: error.config?.url,
        data: error.config?.data,
        headers: error.config?.headers,
        timeout: error.config?.timeout
      });
    }
    
    if (error.code) {
      console.error('Error code:', error.code);
    }
    
    console.error('Full error stack:', error.stack);
  }
}

// Run the test
testDirectBooking().catch(console.error);
