/**
 * Test script to verify public booking access
 * Tests that /book/* routes are accessible without authentication
 */

const axios = require('axios');

async function testPublicBookingAccess() {
  console.log('🧪 Testing Public Booking Access');
  console.log('================================');

  const tests = [
    {
      name: 'Backend API - Get form by slug',
      url: 'http://localhost:3000/api/public/forms/cng-ty-tnhh',
      method: 'GET',
      expectedStatus: 200
    },
    {
      name: 'Frontend - Booking page',
      url: 'http://localhost:4000/book/cng-ty-tnhh',
      method: 'GET',
      expectedStatus: 200
    },
    {
      name: 'Backend API - Submit booking (test data)',
      url: 'http://localhost:3000/api/public/bookings',
      method: 'POST',
      data: {
        formSlug: 'cng-ty-tnhh',
        customerName: 'Test Customer',
        phoneNumber: '0123456789',
        emailAddress: '<EMAIL>',
        preferredDate: '2024-12-25',
        preferredTime: '10:00',
        specialRequests: 'Test booking from script'
      },
      expectedStatus: 201
    }
  ];

  let passedTests = 0;
  let totalTests = tests.length;

  for (const test of tests) {
    try {
      console.log(`\n📋 ${test.name}`);
      console.log(`   URL: ${test.url}`);
      console.log(`   Method: ${test.method}`);

      const config = {
        method: test.method,
        url: test.url,
        timeout: 10000,
        validateStatus: () => true // Don't throw on any status code
      };

      if (test.data) {
        config.data = test.data;
        config.headers = { 'Content-Type': 'application/json' };
        console.log(`   Data: ${JSON.stringify(test.data, null, 2)}`);
      }

      const response = await axios(config);
      
      console.log(`   Status: ${response.status}`);
      console.log(`   Expected: ${test.expectedStatus}`);

      if (response.status === test.expectedStatus) {
        console.log('   ✅ PASSED');
        passedTests++;
        
        // Log response data for successful API calls
        if (test.url.includes('/api/')) {
          console.log(`   Response: ${JSON.stringify(response.data, null, 2).substring(0, 200)}...`);
        }
      } else {
        console.log('   ❌ FAILED');
        console.log(`   Error: Expected status ${test.expectedStatus}, got ${response.status}`);
        if (response.data) {
          console.log(`   Response: ${JSON.stringify(response.data, null, 2)}`);
        }
      }
    } catch (error) {
      console.log('   ❌ FAILED');
      console.log(`   Error: ${error.message}`);
      if (error.response) {
        console.log(`   Status: ${error.response.status}`);
        console.log(`   Response: ${JSON.stringify(error.response.data, null, 2)}`);
      }
    }
  }

  console.log('\n📊 Test Results');
  console.log('================');
  console.log(`Passed: ${passedTests}/${totalTests}`);
  console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);

  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! Public booking access is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Please check the issues above.');
  }

  return passedTests === totalTests;
}

// Run the test
if (require.main === module) {
  testPublicBookingAccess()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { testPublicBookingAccess };
