/**
 * Booking Routes
 * Defines API endpoints for booking management
 */

const express = require('express');
const router = express.Router();
const BookingController = require('./controller');
const { authenticate, authorize } = require('../../middleware/auth');
const { validateRequest } = require('../../middleware/validation');
const { validateIdParam } = require('../../middleware/validation');
const { generalLimiter: rateLimiter, adminLimiter } = require('../../middleware/rateLimiter');

// Validation schemas
const { body, query } = require('express-validator');

// Create booking validation
const createBookingValidation = [
  body('customerId')
    .isInt({ min: 1 })
    .withMessage('Valid customer ID is required'),
  body('serviceId')
    .isInt({ min: 1 })
    .withMessage('Valid service ID is required'),
  body('branchId')
    .isInt({ min: 1 })
    .withMessage('Valid branch ID is required'),
  body('employeeId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid employee ID is required'),
  body('bookingDate')
    .isISO8601()
    .withMessage('Valid booking date is required (YYYY-MM-DD)')
    .custom((value) => {
      const bookingDate = new Date(value);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (bookingDate < today) {
        throw new Error('Booking date cannot be in the past');
      }
      return true;
    }),
  body('startTime')
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage('Valid start time is required (HH:MM format)'),
  body('discountAmount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Discount amount must be a positive number'),
  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Notes must not exceed 1000 characters'),
  body('customerNotes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Customer notes must not exceed 500 characters'),
  body('internalNotes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Internal notes must not exceed 500 characters')
];

// Update booking validation
const updateBookingValidation = [
  body('employeeId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid employee ID is required'),
  body('startTime')
    .optional()
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage('Valid start time is required (HH:MM format)'),
  body('discountAmount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Discount amount must be a positive number'),
  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Notes must not exceed 1000 characters'),
  body('customerNotes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Customer notes must not exceed 500 characters'),
  body('internalNotes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Internal notes must not exceed 500 characters')
];

// Cancel booking validation
const cancelBookingValidation = [
  body('reason')
    .notEmpty()
    .withMessage('Cancellation reason is required')
    .isLength({ min: 5, max: 500 })
    .withMessage('Cancellation reason must be between 5 and 500 characters')
];

// Complete booking validation
const completeBookingValidation = [
  body('rating')
    .optional()
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),
  body('review')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Review must not exceed 1000 characters')
];

// Query validation for filtering
const filterValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('status')
    .optional()
    .isIn(['pending', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show'])
    .withMessage('Invalid status value'),
  query('customerId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid customer ID is required'),
  query('serviceId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid service ID is required'),
  query('branchId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid branch ID is required'),
  query('employeeId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid employee ID is required'),
  query('bookingDate')
    .optional()
    .isISO8601()
    .withMessage('Valid booking date is required (YYYY-MM-DD)'),
  query('startDate')
    .optional()
    .isISO8601()
    .withMessage('Valid start date is required (YYYY-MM-DD)'),
  query('endDate')
    .optional()
    .isISO8601()
    .withMessage('Valid end date is required (YYYY-MM-DD)'),
  query('paymentStatus')
    .optional()
    .isIn(['pending', 'partial', 'paid', 'refunded'])
    .withMessage('Invalid payment status value')
];

// Available slots validation
const availableSlotsValidation = [
  query('branchId')
    .isInt({ min: 1 })
    .withMessage('Valid branch ID is required'),
  query('serviceId')
    .isInt({ min: 1 })
    .withMessage('Valid service ID is required'),
  query('employeeId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid employee ID is required'),
  query('date')
    .isISO8601()
    .withMessage('Valid date is required (YYYY-MM-DD)')
    .custom((value) => {
      const date = new Date(value);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (date < today) {
        throw new Error('Date cannot be in the past');
      }
      return true;
    })
];

/**
 * @route   GET /api/bookings/stats
 * @desc    Get booking statistics
 * @access  Private (Admin, Staff)
 */
router.get('/stats',
  authenticate,
  authorize(['admin', 'staff']),
  rateLimiter,
  BookingController.getBookingStats
);

/**
 * @route   GET /api/bookings/available-slots
 * @desc    Get available time slots
 * @access  Private
 */
router.get('/available-slots',
  authenticate,
  rateLimiter,
  availableSlotsValidation,
  validateRequest,
  BookingController.getAvailableTimeSlots
);

/**
 * @route   GET /api/bookings/code/:code
 * @desc    Get booking by booking code
 * @access  Private
 */
router.get('/code/:code',
  authenticate,
  rateLimiter,
  BookingController.getBookingByCode
);

/**
 * @route   GET /api/bookings
 * @desc    Get all bookings with pagination and filtering
 * @access  Private (Admin, Staff, Customer)
 * @note    User-based filtering is applied in the service layer:
 *          - Admin: sees all bookings
 *          - Staff: sees bookings for their managed branches
 *          - Customer: sees only their own bookings
 */
router.get('/',
  authenticate,
  // Remove authorize middleware to allow customers
  // Authorization is handled in service layer with user-based filtering
  rateLimiter,
  filterValidation,
  validateRequest,
  BookingController.getAllBookings
);

/**
 * @route   POST /api/bookings
 * @desc    Create a new booking
 * @access  Private
 */
router.post('/',
  authenticate,
  rateLimiter,
  createBookingValidation,
  validateRequest,
  BookingController.createBooking
);

/**
 * @route   GET /api/bookings/:id
 * @desc    Get booking by ID
 * @access  Private
 */
router.get('/:id',
  authenticate,
  rateLimiter,
  validateIdParam('id'),
  validateRequest,
  BookingController.getBookingById
);

/**
 * @route   PUT /api/bookings/:id
 * @desc    Update booking
 * @access  Private (Admin, Staff, or booking owner)
 */
router.put('/:id',
  authenticate,
  rateLimiter,
  validateIdParam('id'),
  updateBookingValidation,
  validateRequest,
  BookingController.updateBooking
);

/**
 * @route   PATCH /api/bookings/:id/cancel
 * @desc    Cancel booking
 * @access  Private (Admin, Staff, or booking owner)
 */
router.patch('/:id/cancel',
  authenticate,
  rateLimiter,
  validateIdParam('id'),
  cancelBookingValidation,
  validateRequest,
  BookingController.cancelBooking
);

/**
 * @route   PATCH /api/bookings/:id/confirm
 * @desc    Confirm booking
 * @access  Private (Admin, Staff)
 */
router.patch('/:id/confirm',
  authenticate,
  authorize(['admin', 'staff']),
  rateLimiter,
  validateIdParam('id'),
  validateRequest,
  BookingController.confirmBooking
);

/**
 * @route   PATCH /api/bookings/:id/complete
 * @desc    Complete booking
 * @access  Private (Admin, Staff)
 */
router.patch('/:id/complete',
  authenticate,
  authorize(['admin', 'staff']),
  rateLimiter,
  validateIdParam('id'),
  completeBookingValidation,
  validateRequest,
  BookingController.completeBooking
);

/**
 * @route   DELETE /api/bookings/:id
 * @desc    Delete booking (soft delete)
 * @access  Private (Admin)
 */
router.delete('/:id',
  authenticate,
  authorize(['admin']),
  adminLimiter,
  validateIdParam('id'),
  validateRequest,
  BookingController.deleteBooking
);

module.exports = router;
