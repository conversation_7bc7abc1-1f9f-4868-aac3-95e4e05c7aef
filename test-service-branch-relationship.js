const axios = require('axios');

async function testServiceBranchRelationship() {
  console.log('🧪 Testing service-branch relationship for form creation...');
  
  const API_BASE = 'http://localhost:3000/api';
  
  try {
    // 1. Login to get token
    console.log('\n1. Logging in...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123456'
    });
    
    const token = loginResponse.data.data.token;
    console.log('✅ Login successful');
    
    // 2. Get user's branches
    console.log('\n2. Getting user\'s branches...');
    const branchesResponse = await axios.get(`${API_BASE}/branches`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    const branches = branchesResponse.data.data;
    console.log(`✅ Found ${branches.length} branches managed by user`);
    
    branches.forEach(branch => {
      console.log(`   - Branch: ${branch.name} (ID: ${branch.id})`);
    });
    
    // 3. Get user's services
    console.log('\n3. Getting user\'s services...');
    const servicesResponse = await axios.get(`${API_BASE}/services`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    const services = servicesResponse.data.data;
    console.log(`✅ Found ${services.length} services`);
    
    services.forEach(service => {
      console.log(`   - Service: ${service.name} (ID: ${service.id}, Branch: ${service.branchId || service.branch_id})`);
    });
    
    // 4. Find matching service-branch pairs
    console.log('\n4. Finding valid service-branch pairs...');
    const validPairs = [];
    
    for (const service of services) {
      const serviceBranchId = service.branchId || service.branch_id;
      const matchingBranch = branches.find(b => b.id === serviceBranchId);
      
      if (matchingBranch) {
        validPairs.push({
          service: service,
          branch: matchingBranch
        });
        console.log(`✅ Valid pair: Service "${service.name}" (${service.id}) → Branch "${matchingBranch.name}" (${matchingBranch.id})`);
      } else {
        console.log(`⚠️ Invalid pair: Service "${service.name}" (${service.id}) → Branch ID ${serviceBranchId} (not managed by user)`);
      }
    }
    
    if (validPairs.length === 0) {
      console.log('\n❌ No valid service-branch pairs found!');
      console.log('This explains why form creation fails.');
      console.log('\n💡 Solutions:');
      console.log('1. Create a service linked to one of your managed branches');
      console.log('2. Or modify the validation logic to be less restrictive');
      
      // Let's try to create a service linked to the first branch
      if (branches.length > 0) {
        console.log('\n5. Creating a service linked to your branch...');
        const firstBranch = branches[0];
        
        const createServiceData = {
          name: 'Test Service for Form Creation',
          category: 'Test',
          duration: 60,
          price: 100000,
          description: 'Test service for form creation',
          branchId: firstBranch.id
        };
        
        try {
          const createServiceResponse = await axios.post(`${API_BASE}/services`, createServiceData, {
            headers: { 
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });
          
          const newService = createServiceResponse.data.data;
          console.log(`✅ Service created: ${newService.name} (ID: ${newService.id})`);
          
          validPairs.push({
            service: newService,
            branch: firstBranch
          });
          
        } catch (createError) {
          console.log('❌ Failed to create service:', createError.response?.data || createError.message);
        }
      }
    }
    
    // 5. Test form creation with valid pair
    if (validPairs.length > 0) {
      console.log('\n6. Testing form creation with valid service-branch pair...');
      const validPair = validPairs[0];
      
      const createFormData = {
        name: 'Test Form with Valid Pair',
        serviceId: validPair.service.id,
        branchId: validPair.branch.id,
        fields: {
          customerName: true,
          phoneNumber: true,
          emailAddress: true,
          preferredDate: true,
          preferredTime: true,
          specialRequests: true
        }
      };
      
      try {
        const createFormResponse = await axios.post(`${API_BASE}/forms`, createFormData, {
          headers: { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
        
        const newForm = createFormResponse.data.data;
        console.log(`✅ Form created successfully: ${newForm.name} (ID: ${newForm.id})`);
        
        // Test getting the form for editing
        console.log('\n7. Testing form retrieval for editing...');
        const formResponse = await axios.get(`${API_BASE}/forms/${newForm.id}`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        
        const formData = formResponse.data.data;
        console.log('✅ Form retrieved for editing');
        console.log('Form data:', {
          id: formData.id,
          name: formData.name,
          service_id: formData.service_id,
          branch_id: formData.branch_id,
          serviceName: formData.service?.name,
          branchName: formData.branch?.name
        });
        
        // Test the edit page URL
        console.log(`\n🔗 Edit form URL: http://localhost:4000/forms/edit/${newForm.id}`);
        
        // Clean up
        console.log('\n8. Cleaning up test form...');
        try {
          await axios.delete(`${API_BASE}/forms/${newForm.id}`, {
            headers: { Authorization: `Bearer ${token}` }
          });
          console.log('✅ Test form cleaned up');
        } catch (cleanupError) {
          console.log('⚠️ Could not clean up test form:', cleanupError.message);
        }
        
      } catch (createFormError) {
        console.log('❌ Form creation failed:', createFormError.response?.data || createFormError.message);
      }
    }
    
    console.log('\n🎯 Summary:');
    console.log(`- User manages ${branches.length} branches`);
    console.log(`- Found ${services.length} services`);
    console.log(`- Valid service-branch pairs: ${validPairs.length}`);
    
    if (validPairs.length > 0) {
      console.log('\n✅ Pre-populate should work with these valid pairs:');
      validPairs.forEach(pair => {
        console.log(`   - Service: ${pair.service.name} (${pair.service.id}) → Branch: ${pair.branch.name} (${pair.branch.id})`);
      });
    } else {
      console.log('\n❌ No valid pairs found. This is why form creation and editing fails.');
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
    
    console.error('Full error stack:', error.stack);
  }
}

// Run the test
testServiceBranchRelationship().catch(console.error);
