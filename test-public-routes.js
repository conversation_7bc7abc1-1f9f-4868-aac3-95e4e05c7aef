const axios = require('axios');

async function testPublicRoutes() {
  console.log('🧪 Testing public routes without authentication...');
  
  const API_BASE = 'http://localhost:3000/api';
  
  try {
    // 1. Test public form access (should work without auth)
    console.log('\n1. Testing public form access...');
    
    // First, let's get a form slug from authenticated endpoint to test with
    console.log('Getting a form slug for testing...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123456'
    });
    
    const token = loginResponse.data.data.token;
    const formsResponse = await axios.get(`${API_BASE}/forms`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    const forms = formsResponse.data.data.forms;
    if (forms.length === 0) {
      console.log('❌ No forms found to test with');
      return;
    }
    
    const testForm = forms[0];
    const testSlug = testForm.slug;
    console.log(`Using form slug: ${testSlug}`);
    
    // 2. Test public form endpoint WITHOUT authentication
    console.log('\n2. Testing public form endpoint WITHOUT authentication...');
    try {
      const publicFormResponse = await axios.get(`${API_BASE}/public/forms/${testSlug}`);
      console.log('✅ Public form access successful');
      console.log('Response status:', publicFormResponse.status);
      console.log('Form data:', {
        id: publicFormResponse.data.data.id,
        name: publicFormResponse.data.data.name,
        businessName: publicFormResponse.data.data.businessName,
        hasService: !!publicFormResponse.data.data.service,
        hasBranch: !!publicFormResponse.data.data.branch,
        hasFields: !!publicFormResponse.data.data.fields,
        hasBranding: !!publicFormResponse.data.data.branding
      });
    } catch (error) {
      console.log('❌ Public form access failed');
      console.log('Status:', error.response?.status);
      console.log('Error:', error.response?.data || error.message);
    }
    
    // 3. Test public form endpoint WITH authentication (should also work)
    console.log('\n3. Testing public form endpoint WITH authentication...');
    try {
      const publicFormWithAuthResponse = await axios.get(`${API_BASE}/public/forms/${testSlug}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      console.log('✅ Public form access with auth successful');
      console.log('Response status:', publicFormWithAuthResponse.status);
    } catch (error) {
      console.log('❌ Public form access with auth failed');
      console.log('Status:', error.response?.status);
      console.log('Error:', error.response?.data || error.message);
    }
    
    // 4. Test protected form endpoint WITHOUT authentication (should fail)
    console.log('\n4. Testing protected form endpoint WITHOUT authentication...');
    try {
      const protectedFormResponse = await axios.get(`${API_BASE}/forms/${testForm.id}`);
      console.log('⚠️ Protected form access without auth succeeded (unexpected)');
      console.log('Response status:', protectedFormResponse.status);
    } catch (error) {
      console.log('✅ Protected form access without auth failed (expected)');
      console.log('Status:', error.response?.status);
      console.log('Error message:', error.response?.data?.error?.message || error.message);
    }
    
    // 5. Test public booking creation WITHOUT authentication
    console.log('\n5. Testing public booking creation WITHOUT authentication...');
    const bookingData = {
      formSlug: testSlug,
      customerName: 'Test Customer',
      phoneNumber: '0123456789',
      emailAddress: '<EMAIL>',
      preferredDate: '2025-06-25',
      preferredTime: '10:00',
      specialRequests: 'Test booking from public route'
    };
    
    try {
      const publicBookingResponse = await axios.post(`${API_BASE}/public/bookings`, bookingData);
      console.log('✅ Public booking creation successful');
      console.log('Response status:', publicBookingResponse.status);
      console.log('Booking ID:', publicBookingResponse.data.data.id);
    } catch (error) {
      console.log('❌ Public booking creation failed');
      console.log('Status:', error.response?.status);
      console.log('Error:', error.response?.data || error.message);
    }
    
    // 6. Test invalid public form slug
    console.log('\n6. Testing invalid public form slug...');
    try {
      const invalidFormResponse = await axios.get(`${API_BASE}/public/forms/invalid-slug-123`);
      console.log('⚠️ Invalid form access succeeded (unexpected)');
    } catch (error) {
      console.log('✅ Invalid form access failed (expected)');
      console.log('Status:', error.response?.status);
      console.log('Error message:', error.response?.data?.error?.message || error.message);
    }
    
    // 7. Test public booking with missing data
    console.log('\n7. Testing public booking with missing data...');
    const incompleteBookingData = {
      formSlug: testSlug,
      customerName: 'Test Customer'
      // Missing required fields
    };
    
    try {
      const incompleteBookingResponse = await axios.post(`${API_BASE}/public/bookings`, incompleteBookingData);
      console.log('⚠️ Incomplete booking creation succeeded (unexpected)');
    } catch (error) {
      console.log('✅ Incomplete booking creation failed (expected)');
      console.log('Status:', error.response?.status);
      console.log('Error message:', error.response?.data?.error?.message || error.message);
    }
    
    // 8. Test CORS for public routes
    console.log('\n8. Testing CORS headers for public routes...');
    try {
      const corsResponse = await axios.options(`${API_BASE}/public/forms/${testSlug}`);
      console.log('✅ CORS preflight successful');
      console.log('CORS headers present:', {
        'access-control-allow-origin': corsResponse.headers['access-control-allow-origin'],
        'access-control-allow-methods': corsResponse.headers['access-control-allow-methods'],
        'access-control-allow-headers': corsResponse.headers['access-control-allow-headers']
      });
    } catch (error) {
      console.log('⚠️ CORS preflight failed');
      console.log('Status:', error.response?.status);
    }
    
    console.log('\n🎉 Public routes testing completed!');
    console.log('\n📋 Test Results Summary:');
    console.log('- Public form access without auth: Should work ✅');
    console.log('- Public form access with auth: Should work ✅');
    console.log('- Protected form access without auth: Should fail ✅');
    console.log('- Public booking creation: Should work ✅');
    console.log('- Invalid form slug handling: Should fail gracefully ✅');
    console.log('- Incomplete booking data validation: Should fail ✅');
    console.log('- CORS support: Should work ✅');
    
    console.log('\n🔗 Public URLs available:');
    console.log(`- Public Form: ${API_BASE}/public/forms/${testSlug}`);
    console.log(`- Public Booking: ${API_BASE}/public/bookings`);
    console.log(`- Frontend Form: http://localhost:4000/book/${testSlug}`);
    
    console.log('\n✨ Public routes are properly configured and working!');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
    
    console.error('Full error stack:', error.stack);
  }
}

// Run the test
testPublicRoutes().catch(console.error);
