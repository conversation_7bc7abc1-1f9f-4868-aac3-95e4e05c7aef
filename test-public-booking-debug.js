const axios = require('axios');

async function testPublicBookingDebug() {
  console.log('🧪 Testing public booking endpoint with detailed debugging...');
  
  const API_BASE = 'http://localhost:3000/api';
  
  try {
    // 1. First test if we can get a form
    console.log('\n1. Testing form retrieval...');
    
    // Get list of forms first to find a valid slug
    try {
      // Try to login to get forms
      const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
        email: '<EMAIL>',
        password: 'admin123456'
      });
      
      const token = loginResponse.data.data.token;
      
      const formsResponse = await axios.get(`${API_BASE}/forms`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      const forms = formsResponse.data.data.forms;
      console.log(`✅ Found ${forms.length} forms`);
      
      if (forms.length === 0) {
        console.log('❌ No forms found. Please create a form first.');
        return;
      }
      
      const testForm = forms[0];
      console.log(`📝 Using form: ${testForm.name} (slug: ${testForm.slug})`);
      
      // 2. Test getting form by slug (public endpoint)
      console.log('\n2. Testing public form retrieval...');
      const publicFormResponse = await axios.get(`${API_BASE}/public/forms/${testForm.slug}`);
      console.log('✅ Public form retrieved successfully');
      console.log('Form data:', JSON.stringify(publicFormResponse.data.data, null, 2));
      
      // 3. Test booking submission
      console.log('\n3. Testing public booking submission...');
      
      const bookingData = {
        formSlug: testForm.slug,
        customerName: 'Test Customer Debug',
        phoneNumber: '0123456789',
        emailAddress: '<EMAIL>',
        preferredDate: '2025-06-25',
        preferredTime: '10:00',
        specialRequests: 'Test booking from debug script'
      };
      
      console.log('📝 Booking data:', JSON.stringify(bookingData, null, 2));
      
      const bookingResponse = await axios.post(`${API_BASE}/public/bookings`, bookingData, {
        headers: { 'Content-Type': 'application/json' }
      });
      
      console.log('✅ Booking created successfully!');
      console.log('Response:', JSON.stringify(bookingResponse.data, null, 2));
      
    } catch (loginError) {
      console.log('❌ Could not login or get forms:', loginError.message);
      
      // Try with a hardcoded slug if login fails
      console.log('\n2. Testing with hardcoded form slug...');
      const testSlug = 'test-form';
      
      try {
        const publicFormResponse = await axios.get(`${API_BASE}/public/forms/${testSlug}`);
        console.log('✅ Public form retrieved successfully with hardcoded slug');
        
        const bookingData = {
          formSlug: testSlug,
          customerName: 'Test Customer Debug',
          phoneNumber: '0123456789',
          emailAddress: '<EMAIL>',
          preferredDate: '2025-06-25',
          preferredTime: '10:00',
          specialRequests: 'Test booking from debug script'
        };
        
        const bookingResponse = await axios.post(`${API_BASE}/public/bookings`, bookingData, {
          headers: { 'Content-Type': 'application/json' }
        });
        
        console.log('✅ Booking created successfully with hardcoded slug!');
        console.log('Response:', JSON.stringify(bookingResponse.data, null, 2));
        
      } catch (hardcodedError) {
        console.log('❌ Hardcoded slug test failed:', hardcodedError.message);
        if (hardcodedError.response) {
          console.log('Error status:', hardcodedError.response.status);
          console.log('Error data:', JSON.stringify(hardcodedError.response.data, null, 2));
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
      console.error('Response headers:', error.response.headers);
    }
    
    if (error.request) {
      console.error('Request details:', {
        method: error.config?.method,
        url: error.config?.url,
        data: error.config?.data,
        headers: error.config?.headers
      });
    }
    
    console.error('Full error stack:', error.stack);
  }
}

// Run the test
testPublicBookingDebug().catch(console.error);
