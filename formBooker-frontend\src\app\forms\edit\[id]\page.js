'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import DashboardLayout from '@/components/layout/DashboardLayout';
import servicesService from '@/services/servicesService';
import branchesService from '@/services/branchesService';
import FormsService from '@/services/formsService';
import {
  Save,
  Eye,
  Copy,
  Link as LinkIcon,
  Code,
  ArrowLeft,
  Check,
  X,
  Loader2,
  Plus
} from 'lucide-react';

/**
 * Form Edit Page
 * Allows editing existing forms with user-based filtering
 */
export default function EditFormPage() {
  const router = useRouter();
  const params = useParams();
  const formId = params.id;

  const [formData, setFormData] = useState({
    name: '',
    serviceId: '',
    branchId: '',
    status: 'active',
    fields: {
      customerName: true,
      phoneNumber: true,
      emailAddress: true,
      preferredDate: true,
      preferredTime: true,
      specialRequests: false
    },
    fieldsConfig: {},
    brandingConfig: {}
  });

  const [originalForm, setOriginalForm] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState(null);
  const [copySuccess, setCopySuccess] = useState('');
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [savedForm, setSavedForm] = useState(null);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  
  // API data states
  const [services, setServices] = useState([]);
  const [branches, setBranches] = useState([]);
  const [isLoadingServices, setIsLoadingServices] = useState(true);
  const [isLoadingBranches, setIsLoadingBranches] = useState(true);

  // Load form data and initial data on component mount
  useEffect(() => {
    if (formId) {
      loadFormData();
      loadInitialData();
    }
  }, [formId]);

  const loadFormData = async () => {
    try {
      console.log(`🔄 Loading form data for ID: ${formId}`);
      setIsLoading(true);
      setError(null);

      const result = await FormsService.getFormById(formId);
      
      if (result.success && result.data) {
        const form = result.data;
        console.log('✅ Form loaded successfully:', form);
        
        setOriginalForm(form);
        setFormData({
          name: form.name || '',
          serviceId: form.service_id ? form.service_id.toString() : (form.service?.id ? form.service.id.toString() : ''),
          branchId: form.branch_id ? form.branch_id.toString() : (form.branch?.id ? form.branch.id.toString() : ''),
          status: form.status || 'active',
          fields: form.fieldsConfig?.fields || (form.fields_config ?
            (typeof form.fields_config === 'string' ? JSON.parse(form.fields_config) : form.fields_config) : {
            customerName: true,
            phoneNumber: true,
            emailAddress: true,
            preferredDate: true,
            preferredTime: true,
            specialRequests: false
          }),
          fieldsConfig: form.fieldsConfig || (form.fields_config ?
            (typeof form.fields_config === 'string' ? JSON.parse(form.fields_config) : form.fields_config) : {}),
          brandingConfig: form.brandingConfig || (form.branding_config ?
            (typeof form.branding_config === 'string' ? JSON.parse(form.branding_config) : form.branding_config) : {})
        });
      } else {
        console.error('❌ Failed to load form:', result.error);
        setError(result.error || 'Failed to load form');
        
        // Handle specific error types
        if (result.errorType === 'AUTHENTICATION_REQUIRED') {
          router.push('/login');
        } else if (result.errorType === 'NOT_FOUND') {
          setError('Form not found or you do not have permission to edit it');
        }
      }
    } catch (error) {
      console.error('❌ Error loading form:', error);
      setError('Failed to load form. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const loadInitialData = async () => {
    try {
      console.log('🔄 Loading services and branches...');

      // Load services and branches in parallel
      const [servicesResponse, branchesResponse] = await Promise.allSettled([
        servicesService.getServices({ isActive: true, limit: 100 }),
        branchesService.getBranches({ isActive: true, limit: 100 })
      ]);

      // Handle services response
      if (servicesResponse.status === 'fulfilled' && servicesResponse.value.success) {
        const servicesData = servicesResponse.value.data || [];
        setServices(servicesData);
        console.log(`✅ Services loaded: ${servicesData.length} services found`);
      } else {
        console.error('❌ Failed to load services:', servicesResponse.reason);
      }
      setIsLoadingServices(false);

      // Handle branches response
      if (branchesResponse.status === 'fulfilled' && branchesResponse.value.success) {
        const branchesData = branchesResponse.value.data || [];
        setBranches(branchesData);
        console.log(`✅ Branches loaded: ${branchesData.length} branches found`);
      } else {
        console.error('❌ Failed to load branches:', branchesResponse.reason);
      }
      setIsLoadingBranches(false);

    } catch (error) {
      console.error('❌ Error loading initial data:', error);
      setIsLoadingServices(false);
      setIsLoadingBranches(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSave = async () => {
    setIsSaving(true);
    
    try {
      // Validate form data
      if (!formData.name || !formData.serviceId || !formData.branchId) {
        alert('Please fill in all required fields');
        setIsSaving(false);
        return;
      }

      console.log('🔄 Updating form...', formData);

      // Update form using FormsService
      const result = await FormsService.updateForm(formId, formData);
      
      if (result.success) {
        console.log('✅ Form updated successfully:', result.data);
        setSavedForm(result.data);
        setShowSuccessModal(true);

        // Update original form data for comparison
        setOriginalForm(result.data);
      } else {
        console.error('❌ Failed to update form:', result.error);
        
        // Handle different error types
        if (result.errorType === 'AUTHENTICATION_REQUIRED') {
          alert(`Authentication Required: ${result.error}`);
          router.push('/login');
        } else if (result.errorType === 'VALIDATION_ERROR') {
          let message = `Validation Error: ${result.error}`;
          if (result.validationErrors?.length > 0) {
            message += '\n\nDetails:\n' + result.validationErrors.map(err => `- ${err.field}: ${err.message}`).join('\n');
          }
          alert(message);
        } else {
          alert(`Error: ${result.error}`);
        }
      }
    } catch (error) {
      console.error('❌ Error updating form:', error);
      alert('Failed to update form. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleCopy = async (text, type) => {
    try {
      if (!text) {
        setCopySuccess(`Failed to copy ${type}: No content available`);
        setTimeout(() => setCopySuccess(''), 3000);
        return;
      }

      await navigator.clipboard.writeText(text);
      setCopySuccess(`${type} copied to clipboard!`);
      setTimeout(() => setCopySuccess(''), 3000);
      console.log(`${type} copied to clipboard`);
    } catch (err) {
      console.error('Failed to copy:', err);
      setCopySuccess(`Failed to copy ${type}`);
      setTimeout(() => setCopySuccess(''), 3000);
    }
  };

  const selectedService = services.find(s => s.id === parseInt(formData.serviceId));
  const selectedBranch = branches.find(b => b.id === parseInt(formData.branchId));

  // Success Modal Component
  const SuccessModal = () => {
    if (!showSuccessModal || !savedForm) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          {/* Modal Header */}
          <div className="p-6 border-b border-neutral-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                  <Check className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-text">Form Updated Successfully!</h2>
                  <p className="text-neutral-600 text-sm mt-1">Your changes have been saved</p>
                </div>
              </div>
              <button
                onClick={() => setShowSuccessModal(false)}
                className="text-neutral-400 hover:text-neutral-600 transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
          </div>

          {/* Modal Body */}
          <div className="p-6 space-y-6">
            {/* Form Info */}
            <div className="bg-neutral-50 rounded-lg p-4">
              <h3 className="font-medium text-text mb-2">Updated Form Details</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-neutral-600">Name:</span>
                  <span className="font-medium">{savedForm.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-neutral-600">Service:</span>
                  <span className="font-medium">{selectedService?.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-neutral-600">Branch:</span>
                  <span className="font-medium">{selectedBranch?.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-neutral-600">Status:</span>
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    {savedForm.status || 'Active'}
                  </span>
                </div>
              </div>
            </div>

            {/* Share Options */}
            {originalForm?.publicUrl && (
              <div className="space-y-4">
                <h3 className="font-medium text-text">🔗 Share Your Updated Form</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Direct Link */}
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <LinkIcon className="h-4 w-4 text-primary-600 mr-2" />
                        <span className="text-sm font-medium text-neutral-700">Direct Link</span>
                      </div>
                      <button
                        onClick={() => handleCopy(originalForm.publicUrl, 'Direct Link')}
                        className="btn-outline btn-sm"
                      >
                        <Copy className="h-4 w-4 mr-1" />
                        Copy
                      </button>
                    </div>
                    <div className="p-3 bg-neutral-50 rounded-lg text-xs text-neutral-600 break-all border">
                      {originalForm.publicUrl}
                    </div>
                  </div>

                  {/* Embed Code */}
                  {originalForm?.embedCode && (
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <Code className="h-4 w-4 text-accent-600 mr-2" />
                          <span className="text-sm font-medium text-neutral-700">Embed Code</span>
                        </div>
                        <button
                          onClick={() => handleCopy(originalForm.embedCode, 'Embed Code')}
                          className="btn-outline btn-sm"
                        >
                          <Copy className="h-4 w-4 mr-1" />
                          Copy
                        </button>
                      </div>
                      <div className="p-3 bg-neutral-50 rounded-lg text-xs text-neutral-600 break-all border font-mono max-h-20 overflow-y-auto">
                        {originalForm.embedCode}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Modal Footer */}
          <div className="p-6 border-t border-neutral-200 bg-neutral-50 rounded-b-lg">
            <div className="flex justify-between items-center">
              <button
                onClick={() => router.push('/forms')}
                className="btn-outline"
              >
                View All Forms
              </button>
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowSuccessModal(false)}
                  className="btn-outline"
                >
                  Continue Editing
                </button>
                {originalForm?.publicUrl && (
                  <button
                    onClick={() => window.open(originalForm.publicUrl, '_blank')}
                    className="btn-primary"
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Preview Form
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Loading state
  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary-600" />
            <p className="text-neutral-600">Loading form...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Error state
  if (error) {
    return (
      <DashboardLayout>
        <div className="max-w-4xl mx-auto p-6">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
            <X className="h-12 w-12 text-red-600 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-red-800 mb-2">Error Loading Form</h2>
            <p className="text-red-700 mb-4">{error}</p>
            <div className="flex justify-center space-x-3">
              <button
                onClick={() => router.push('/forms')}
                className="btn-outline"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Forms
              </button>
              <button
                onClick={() => window.location.reload()}
                className="btn-primary"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      {/* Success Modal */}
      <SuccessModal />

      <div className="py-6">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center">
              <button
                onClick={() => router.push('/forms')}
                className="btn-ghost btn-sm mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back
              </button>
              <div>
                <h1 className="text-3xl font-bold text-text">Edit Booking Form</h1>
                <p className="mt-2 text-neutral-600">
                  Update your booking form settings and configuration
                </p>
              </div>
            </div>
          </div>

          {/* Copy Success Toast */}
          {copySuccess && (
            <div className="fixed top-4 right-4 z-50 p-4 bg-green-50 border border-green-200 rounded-lg shadow-lg">
              <div className="flex items-center">
                <Check className="h-5 w-5 text-green-500 mr-2" />
                <p className="text-green-700 font-medium">{copySuccess}</p>
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Form Builder */}
            <div className="space-y-6">
              {/* Form Settings */}
              <div className="card">
                <div className="card-header">
                  <h2 className="text-lg font-semibold text-text">Form Settings</h2>
                  <p className="text-sm text-neutral-500 mt-1">Update your booking form details</p>
                </div>
                <div className="card-body space-y-4">
                  {/* Form Name */}
                  <div>
                    <label className="form-label">Form Name *</label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className="form-input"
                      placeholder="e.g., Hair Cut Booking Form"
                    />
                    <p className="text-xs text-neutral-500 mt-1">Give your form a descriptive name</p>
                  </div>

                  {/* Service Selection */}
                  <div>
                    <label className="form-label">Service *</label>
                    {isLoadingServices ? (
                      <div className="form-input flex items-center">
                        <div className="w-4 h-4 border-2 border-primary-600 border-t-transparent rounded-full animate-spin mr-2"></div>
                        Loading services...
                      </div>
                    ) : (
                      <select
                        name="serviceId"
                        value={formData.serviceId}
                        onChange={handleInputChange}
                        className="form-input"
                      >
                        <option value="">Choose a service for this form</option>
                        {services.length === 0 ? (
                          <option disabled>No services available</option>
                        ) : (
                          services.map(service => (
                            <option key={service.id} value={service.id}>
                              {service.name} - ${service.price} ({service.duration} min)
                            </option>
                          ))
                        )}
                      </select>
                    )}
                    <p className="text-xs text-neutral-500 mt-1">Select the service customers will book</p>
                  </div>

                  {/* Branch Selection */}
                  <div>
                    <label className="form-label">Branch *</label>
                    {isLoadingBranches ? (
                      <div className="form-input flex items-center">
                        <div className="w-4 h-4 border-2 border-primary-600 border-t-transparent rounded-full animate-spin mr-2"></div>
                        Loading branches...
                      </div>
                    ) : (
                      <select
                        name="branchId"
                        value={formData.branchId}
                        onChange={handleInputChange}
                        className="form-input"
                      >
                        <option value="">Choose a branch location</option>
                        {branches.length === 0 ? (
                          <option disabled>No branches available</option>
                        ) : (
                          branches.map(branch => (
                            <option key={branch.id} value={branch.id}>
                              {branch.name} - {branch.address}
                            </option>
                          ))
                        )}
                      </select>
                    )}
                    <p className="text-xs text-neutral-500 mt-1">Select the branch where service will be provided</p>
                  </div>

                  {/* Status */}
                  <div>
                    <label className="form-label">Status</label>
                    <select
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                      className="form-input"
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                      <option value="draft">Draft</option>
                    </select>
                    <p className="text-xs text-neutral-500 mt-1">Control form availability</p>
                  </div>
                </div>
              </div>

              {/* Form Fields */}
              <div className="card">
                <div className="card-header">
                  <h2 className="text-lg font-semibold text-text">Form Fields</h2>
                  <p className="text-sm text-neutral-500 mt-1">Your form includes these standard booking fields</p>
                </div>
                <div className="card-body">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg">
                      <Check className="h-5 w-5 text-green-600 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-green-800">Customer Name</p>
                        <p className="text-xs text-green-600">Required field</p>
                      </div>
                    </div>

                    <div className="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg">
                      <Check className="h-5 w-5 text-green-600 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-green-800">Phone Number</p>
                        <p className="text-xs text-green-600">Required field</p>
                      </div>
                    </div>

                    <div className="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg">
                      <Check className="h-5 w-5 text-green-600 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-green-800">Email Address</p>
                        <p className="text-xs text-green-600">Required field</p>
                      </div>
                    </div>

                    <div className="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg">
                      <Check className="h-5 w-5 text-green-600 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-green-800">Preferred Date</p>
                        <p className="text-xs text-green-600">Required field</p>
                      </div>
                    </div>

                    <div className="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg">
                      <Check className="h-5 w-5 text-green-600 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-green-800">Preferred Time</p>
                        <p className="text-xs text-green-600">Required field</p>
                      </div>
                    </div>

                    <div className="flex items-center p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <Check className="h-5 w-5 text-blue-600 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-blue-800">Special Requests</p>
                        <p className="text-xs text-blue-600">Optional field</p>
                      </div>
                    </div>
                  </div>

                  <div className="mt-4 p-3 bg-neutral-50 border border-neutral-200 rounded-lg">
                    <p className="text-sm text-neutral-600">
                      <strong>Note:</strong> These standard fields are automatically included in all booking forms to ensure we collect the necessary information from customers.
                    </p>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="space-y-4">
                {/* Validation Messages */}
                {(!formData.name || !formData.serviceId || !formData.branchId) && (
                  <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-start">
                      <div className="flex-shrink-0">
                        <div className="w-5 h-5 rounded-full bg-yellow-400 flex items-center justify-center">
                          <span className="text-white text-xs font-bold">!</span>
                        </div>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-yellow-800">Complete Required Fields</p>
                        <ul className="mt-1 text-xs text-yellow-700 space-y-1">
                          {!formData.name && <li>• Form name is required</li>}
                          {!formData.serviceId && <li>• Please select a service</li>}
                          {!formData.branchId && <li>• Please select a branch</li>}
                        </ul>
                      </div>
                    </div>
                  </div>
                )}

                <div className="flex space-x-4">
                  <button
                    onClick={() => setIsPreviewOpen(true)}
                    className="btn-outline flex-1"
                    disabled={!formData.name || !formData.serviceId || !formData.branchId || services.length === 0 || branches.length === 0}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Preview Form
                  </button>
                  <button
                    onClick={handleSave}
                    disabled={isSaving || !formData.name || !formData.serviceId || !formData.branchId || services.length === 0 || branches.length === 0}
                    className="btn-primary flex-1"
                  >
                    {isSaving ? (
                      <div className="flex items-center justify-center">
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                        Saving Changes...
                      </div>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Save Changes
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>

            {/* Live Preview */}
            <div className="lg:sticky lg:top-6">
              <div className="card">
                <div className="card-header">
                  <h3 className="text-lg font-semibold text-text">Live Preview</h3>
                  <p className="text-sm text-neutral-500 mt-1">See how your form looks to customers</p>
                </div>
                <div className="card-body">
                  {/* Form Preview */}
                  <div className="border border-neutral-200 rounded-lg p-6 bg-white shadow-sm">
                    <div className="text-center mb-6">
                      <h3 className="text-xl font-semibold text-text mb-2">
                        {formData.name || 'Your Booking Form'}
                      </h3>
                      {selectedService && (
                        <div className="text-sm text-neutral-600 space-y-1 p-3 bg-primary-50 rounded-lg border border-primary-200">
                          <p className="font-medium text-primary-800">Service: {selectedService.name}</p>
                          <p className="text-primary-700">Duration: {selectedService.duration} minutes</p>
                          <p className="text-primary-700">Price: ${selectedService.price}</p>
                        </div>
                      )}
                      {selectedBranch && (
                        <div className="text-sm text-neutral-600 mt-2 p-2 bg-neutral-50 rounded border">
                          <p className="font-medium">📍 Location: {selectedBranch.name}</p>
                          <p className="text-xs">{selectedBranch.address}</p>
                        </div>
                      )}
                    </div>

                    <div className="space-y-4">
                      {formData.fields.customerName && (
                        <div>
                          <label className="form-label">Full Name *</label>
                          <input type="text" className="form-input" placeholder="Enter your full name" disabled />
                        </div>
                      )}
                      {formData.fields.phoneNumber && (
                        <div>
                          <label className="form-label">Phone Number *</label>
                          <input type="tel" className="form-input" placeholder="Enter your phone number" disabled />
                        </div>
                      )}
                      {formData.fields.emailAddress && (
                        <div>
                          <label className="form-label">Email Address *</label>
                          <input type="email" className="form-input" placeholder="Enter your email address" disabled />
                        </div>
                      )}
                      {formData.fields.preferredDate && (
                        <div>
                          <label className="form-label">Preferred Date *</label>
                          <input type="date" className="form-input" disabled />
                        </div>
                      )}
                      {formData.fields.preferredTime && (
                        <div>
                          <label className="form-label">Preferred Time *</label>
                          <input type="time" className="form-input" disabled />
                        </div>
                      )}
                      {formData.fields.specialRequests && (
                        <div>
                          <label className="form-label">Special Requests</label>
                          <textarea className="form-input" rows="3" placeholder="Any special requests or notes..." disabled />
                        </div>
                      )}
                      <button className="btn-primary w-full" disabled>
                        📅 Book My Appointment
                      </button>
                    </div>

                    <div className="text-center mt-6 pt-4 border-t border-neutral-200">
                      <p className="text-xs text-neutral-400">Powered by FormBooker</p>
                    </div>
                  </div>

                  {/* Form Information */}
                  {originalForm && (
                    <div className="mt-6 p-4 bg-neutral-50 rounded-lg border border-neutral-200">
                      <h4 className="text-sm font-semibold text-neutral-700 mb-3">Form Information</h4>
                      <div className="space-y-2 text-xs">
                        <div className="flex justify-between">
                          <span className="text-neutral-600">Form ID:</span>
                          <span className="font-medium">{originalForm.id}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-neutral-600">Created:</span>
                          <span className="font-medium">
                            {originalForm.createdAt ? new Date(originalForm.createdAt).toLocaleDateString() : 'N/A'}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-neutral-600">Last Updated:</span>
                          <span className="font-medium">
                            {originalForm.updatedAt ? new Date(originalForm.updatedAt).toLocaleDateString() : 'N/A'}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-neutral-600">Status:</span>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            formData.status === 'active' ? 'bg-green-100 text-green-800' :
                            formData.status === 'inactive' ? 'bg-red-100 text-red-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {formData.status}
                          </span>
                        </div>
                      </div>

                      {/* Quick Actions */}
                      {originalForm.publicUrl && (
                        <div className="mt-4 pt-3 border-t border-neutral-300">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleCopy(originalForm.publicUrl, 'Public URL')}
                              className="btn-outline btn-xs flex-1"
                            >
                              <Copy className="h-3 w-3 mr-1" />
                              Copy Link
                            </button>
                            <button
                              onClick={() => window.open(originalForm.publicUrl, '_blank')}
                              className="btn-outline btn-xs flex-1"
                            >
                              <Eye className="h-3 w-3 mr-1" />
                              Preview
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
