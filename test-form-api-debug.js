const axios = require('axios');

async function testFormApiDebug() {
  console.log('🧪 Testing form API to debug serviceId and branchId...');
  
  const API_BASE = 'http://localhost:3000/api';
  
  try {
    // 1. Login to get token
    console.log('\n1. Logging in...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123456'
    });
    
    const token = loginResponse.data.data.token;
    console.log('✅ Login successful');
    
    // 2. Get forms list to find a form
    console.log('\n2. Getting forms list...');
    const formsResponse = await axios.get(`${API_BASE}/forms`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    const forms = formsResponse.data.data.forms;
    console.log(`✅ Found ${forms.length} forms`);
    
    if (forms.length === 0) {
      console.log('❌ No forms found. Please create a form first.');
      return;
    }
    
    const testForm = forms[0];
    console.log(`📝 Using form: ${testForm.name} (ID: ${testForm.id})`);
    console.log('Form from list:', {
      id: testForm.id,
      name: testForm.name,
      serviceId: testForm.serviceId,
      branchId: testForm.branchId,
      service_id: testForm.service_id,
      branch_id: testForm.branch_id,
      status: testForm.status,
      hasService: !!testForm.service,
      hasBranch: !!testForm.branch
    });
    
    // 3. Get specific form by ID to see detailed response
    console.log('\n3. Getting form by ID for detailed analysis...');
    const formResponse = await axios.get(`${API_BASE}/forms/${testForm.id}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    const formData = formResponse.data.data;
    console.log('✅ Form retrieved by ID');
    console.log('Full form response keys:', Object.keys(formData));
    console.log('Form data analysis:', {
      id: formData.id,
      name: formData.name,
      
      // Check all possible service ID fields
      serviceId: formData.serviceId,
      service_id: formData.service_id,
      
      // Check all possible branch ID fields
      branchId: formData.branchId,
      branch_id: formData.branch_id,
      
      // Check status
      status: formData.status,
      
      // Check associations
      hasService: !!formData.service,
      hasBranch: !!formData.branch,
      hasOwner: !!formData.owner,
      
      // Service details if exists
      serviceDetails: formData.service ? {
        id: formData.service.id,
        name: formData.service.name,
        price: formData.service.price,
        duration: formData.service.duration
      } : null,
      
      // Branch details if exists
      branchDetails: formData.branch ? {
        id: formData.branch.id,
        name: formData.branch.name,
        address: formData.branch.address
      } : null,
      
      // Config fields
      hasFieldsConfig: !!formData.fieldsConfig,
      hasBrandingConfig: !!formData.brandingConfig,
      fields_config: formData.fields_config,
      branding_config: formData.branding_config,
      
      // URLs
      hasPublicUrl: !!formData.publicUrl,
      hasEmbedCode: !!formData.embedCode
    });
    
    // 4. Check raw database response by looking at toJSON
    console.log('\n4. Raw form data structure:');
    console.log('Raw form keys:', Object.keys(formData));
    
    // 5. Test services and branches endpoints
    console.log('\n5. Testing services and branches endpoints...');
    const [servicesResponse, branchesResponse] = await Promise.allSettled([
      axios.get(`${API_BASE}/services`, { headers: { Authorization: `Bearer ${token}` } }),
      axios.get(`${API_BASE}/branches`, { headers: { Authorization: `Bearer ${token}` } })
    ]);
    
    if (servicesResponse.status === 'fulfilled' && servicesResponse.value.data.success) {
      const services = servicesResponse.value.data.data;
      console.log(`✅ Services loaded: ${services.length} services`);
      
      // Find service that matches form's service
      const matchingService = services.find(s => 
        s.id === formData.serviceId || 
        s.id === formData.service_id || 
        (formData.service && s.id === formData.service.id)
      );
      
      if (matchingService) {
        console.log(`✅ Matching service found: ${matchingService.name} (ID: ${matchingService.id})`);
      } else {
        console.log('⚠️ No matching service found in services list');
        console.log('Available service IDs:', services.map(s => s.id));
        console.log('Form service references:', {
          serviceId: formData.serviceId,
          service_id: formData.service_id,
          serviceAssociation: formData.service?.id
        });
      }
    }
    
    if (branchesResponse.status === 'fulfilled' && branchesResponse.value.data.success) {
      const branches = branchesResponse.value.data.data;
      console.log(`✅ Branches loaded: ${branches.length} branches`);
      
      // Find branch that matches form's branch
      const matchingBranch = branches.find(b => 
        b.id === formData.branchId || 
        b.id === formData.branch_id || 
        (formData.branch && b.id === formData.branch.id)
      );
      
      if (matchingBranch) {
        console.log(`✅ Matching branch found: ${matchingBranch.name} (ID: ${matchingBranch.id})`);
      } else {
        console.log('⚠️ No matching branch found in branches list');
        console.log('Available branch IDs:', branches.map(b => b.id));
        console.log('Form branch references:', {
          branchId: formData.branchId,
          branch_id: formData.branch_id,
          branchAssociation: formData.branch?.id
        });
      }
    }
    
    console.log('\n🎯 Debug Summary:');
    console.log('- Form ID fields present:', {
      serviceId: !!formData.serviceId,
      service_id: !!formData.service_id,
      branchId: !!formData.branchId,
      branch_id: !!formData.branch_id
    });
    console.log('- Association objects present:', {
      service: !!formData.service,
      branch: !!formData.branch
    });
    console.log('- Recommended approach: Use association objects (service.id, branch.id) for dropdown pre-selection');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
    
    console.error('Full error stack:', error.stack);
  }
}

// Run the test
testFormApiDebug().catch(console.error);
