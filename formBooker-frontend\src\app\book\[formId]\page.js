'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import FormsService from '@/services/formsService';
import { 
  Calendar, 
  Clock, 
  MapPin, 
  DollarSign,
  CheckCircle,
  ArrowLeft,
  User,
  Phone,
  Mail
} from 'lucide-react';

/**
 * Public Booking Form Page
 * Implements the exact wireframe from plan-ui-new.md
 */
export default function BookingFormPage() {
  const params = useParams();
  const router = useRouter();
  const [formData, setFormData] = useState({
    fullName: '',
    phone: '',
    email: '',
    preferredDate: '',
    preferredTime: '',
    specialRequests: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [errors, setErrors] = useState({});

  // Mock form configuration - TODO: Fetch from API based on formId
  const [formConfig, setFormConfig] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchFormConfig = async () => {
      try {
        setLoading(true);
        
        // Fetch actual form data by slug
        const result = await FormsService.getFormBySlug(params.formId);
        
        if (result.success) {
          const formData = result.data;
          
          // Transform API response to component format
          const config = {
            id: formData.id,
            name: formData.name,
            businessName: formData.businessName,
            service: {
              name: formData.service.name,
              duration: formData.service.duration,
              price: formData.service.price,
              description: formData.service.description
            },
            branch: {
              name: formData.branch.name,
              address: formData.branch.address,
              phone: formData.branch.phone,
              city: formData.branch.city
            },
            fields: formData.fields,
            branding: formData.branding
          };
          
          setFormConfig(config);
        } else {
          console.error('Failed to load form:', result.error);
          setFormConfig(null);
        }
      } catch (error) {
        console.error('Error fetching form config:', error);
        setFormConfig(null);
      } finally {
        setLoading(false);
      }
    };

    if (params.formId) {
      fetchFormConfig();
    }
  }, [params.formId]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.fullName.trim()) {
      newErrors.fullName = 'Full name is required';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email address is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formData.preferredDate) {
      newErrors.preferredDate = 'Preferred date is required';
    }

    if (!formData.preferredTime) {
      newErrors.preferredTime = 'Preferred time is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Submit booking using the API
      const result = await FormsService.submitPublicBooking({
        formSlug: params.formId,
        customerName: formData.fullName,
        phoneNumber: formData.phone,
        emailAddress: formData.email,
        preferredDate: formData.preferredDate,
        preferredTime: formData.preferredTime,
        specialRequests: formData.specialRequests
      });

      if (result.success) {
        setIsSubmitted(true);
      } else {
        setErrors({ submit: result.error || 'Failed to submit booking. Please try again.' });
      }
    } catch (error) {
      console.error('Booking submission error:', error);
      setErrors({ submit: 'Failed to submit booking. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-gray-200 border-t-primary-500 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-neutral-600">Loading booking form...</p>
        </div>
      </div>
    );
  }

  if (!formConfig) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-text mb-2">Form Not Found</h1>
          <p className="text-neutral-600">The booking form you're looking for doesn't exist.</p>
        </div>
      </div>
    );
  }

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center py-12 px-4">
        <div className="max-w-lg w-full">
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 text-center overflow-hidden">
            <div className="bg-gradient-to-r from-green-500 to-emerald-500 px-6 py-8">
              <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                <CheckCircle className="h-12 w-12 text-green-500" />
              </div>

              <h1 className="text-3xl font-bold text-white mb-2">
                Booking Confirmed!
              </h1>

              <p className="text-xl text-green-50">
                Thank you, {formData.fullName}!
              </p>
            </div>

            <div className="p-8">

              <div className="bg-gray-50 rounded-xl p-6 mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">Your Appointment Details</h3>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center mr-4">
                      <Calendar className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Service</p>
                      <p className="font-medium text-gray-900">{formConfig.service.name}</p>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center mr-4">
                      <Calendar className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Date</p>
                      <p className="font-medium text-gray-900">{new Date(formData.preferredDate).toLocaleDateString('en-US', {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}</p>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center mr-4">
                      <Clock className="h-5 w-5 text-purple-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Time</p>
                      <p className="font-medium text-gray-900">{formData.preferredTime}</p>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-orange-100 rounded-xl flex items-center justify-center mr-4">
                      <MapPin className="h-5 w-5 text-orange-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Location</p>
                      <p className="font-medium text-gray-900">{formConfig.branch.name}</p>
                      <p className="text-sm text-gray-500">{formConfig.branch.address}</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6">
                <p className="text-blue-800 text-center">
                  We'll contact you soon to confirm your appointment.
                </p>
              </div>

              <div className="space-y-4">
                <div className="text-center">
                  <p className="text-sm text-gray-600">
                    Questions? Call <a href={`tel:${formConfig.branch.phone}`} className="font-medium text-blue-600 hover:text-blue-700">{formConfig.branch.phone}</a>
                  </p>
                </div>

                <button
                  onClick={() => window.location.reload()}
                  className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-[1.02] shadow-lg"
                >
                  Book Another Service
                </button>
              </div>
            </div>
          </div>

          {/* Powered by FormBooker */}
          <div className="text-center mt-8">
            <p className="text-xs text-gray-400">
              Powered by <span className="font-medium text-blue-600">FormBooker</span>
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="max-w-lg mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-10">
          <div className="mb-6">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
              <Calendar className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2 tracking-tight">
              {formConfig.businessName}
            </h1>
            <h2 className="text-xl text-gray-600 font-medium">
              {formConfig.name}
            </h2>
          </div>
        </div>

        {/* Service Info */}
        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 mb-8 overflow-hidden">
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
            <h3 className="font-semibold text-white text-lg">{formConfig.service.name}</h3>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 gap-4">
              <div className="flex items-center text-gray-700">
                <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center mr-4">
                  <Clock className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-500">Duration</p>
                  <p className="font-medium">{formConfig.service.duration} minutes</p>
                </div>
              </div>
              <div className="flex items-center text-gray-700">
                <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center mr-4">
                  <MapPin className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-500">Location</p>
                  <p className="font-medium">{formConfig.branch.name}</p>
                  <p className="text-sm text-gray-500">{formConfig.branch.address}</p>
                </div>
              </div>
              <div className="flex items-center text-gray-700">
                <div className="w-10 h-10 bg-emerald-100 rounded-xl flex items-center justify-center mr-4">
                  <DollarSign className="h-5 w-5 text-emerald-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-500">Price</p>
                  <p className="font-medium text-lg">${formConfig.service.price}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Booking Form */}
        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
          <div className="px-6 py-5 border-b border-gray-100">
            <h3 className="text-xl font-semibold text-gray-900 flex items-center">
              <User className="h-5 w-5 mr-2 text-blue-600" />
              Your Information
            </h3>
            <p className="text-sm text-gray-500 mt-1">Please fill in your details to book the appointment</p>
          </div>
          <div className="p-6">
            
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Full Name */}
              {formConfig.fields.customerName && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <User className="h-4 w-4 inline mr-2" />
                    Full Name
                  </label>
                  <input
                    type="text"
                    name="fullName"
                    value={formData.fullName}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 ${errors.fullName ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                    placeholder="Enter your full name"
                  />
                  {errors.fullName && (
                    <p className="mt-2 text-sm text-red-600">{errors.fullName}</p>
                  )}
                </div>
              )}

              {/* Phone */}
              {formConfig.fields.phoneNumber && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Phone className="h-4 w-4 inline mr-2" />
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 ${errors.phone ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                    placeholder="Enter your phone number"
                  />
                  {errors.phone && (
                    <p className="mt-2 text-sm text-red-600">{errors.phone}</p>
                  )}
                </div>
              )}

              {/* Email */}
              {formConfig.fields.emailAddress && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Mail className="h-4 w-4 inline mr-2" />
                    Email Address
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 ${errors.email ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                    placeholder="Enter your email address"
                  />
                  {errors.email && (
                    <p className="mt-2 text-sm text-red-600">{errors.email}</p>
                  )}
                </div>
              )}

              {/* Preferred Date */}
              {formConfig.fields.preferredDate && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Calendar className="h-4 w-4 inline mr-2" />
                    Preferred Date
                  </label>
                  <input
                    type="date"
                    name="preferredDate"
                    value={formData.preferredDate}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 ${errors.preferredDate ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                    min={new Date().toISOString().split('T')[0]}
                  />
                  {errors.preferredDate && (
                    <p className="mt-2 text-sm text-red-600">{errors.preferredDate}</p>
                  )}
                </div>
              )}

              {/* Preferred Time */}
              {formConfig.fields.preferredTime && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Clock className="h-4 w-4 inline mr-2" />
                    Preferred Time
                  </label>
                  <input
                    type="time"
                    name="preferredTime"
                    value={formData.preferredTime}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 ${errors.preferredTime ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                  />
                  {errors.preferredTime && (
                    <p className="mt-2 text-sm text-red-600">{errors.preferredTime}</p>
                  )}
                </div>
              )}

              {/* Special Requests */}
              {formConfig.fields.specialRequests && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Special Requests (Optional)
                  </label>
                  <textarea
                    name="specialRequests"
                    value={formData.specialRequests}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 resize-none"
                    rows="3"
                    placeholder="Any special requests or notes..."
                  />
                </div>
              )}

              {/* Submit Error */}
              {errors.submit && (
                <div className="bg-red-50 border border-red-200 rounded-xl p-4">
                  <p className="text-sm text-red-600">{errors.submit}</p>
                </div>
              )}

              {/* Submit Button */}
              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none shadow-lg"
              >
                {isSubmitting ? (
                  <div className="flex items-center justify-center">
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-3"></div>
                    Booking Appointment...
                  </div>
                ) : (
                  <div className="flex items-center justify-center">
                    <Calendar className="h-5 w-5 mr-2" />
                    Book Appointment
                  </div>
                )}
              </button>
            </form>
          </div>
        </div>

        {/* Powered by FormBooker */}
        <div className="text-center mt-8">
          <p className="text-xs text-gray-400">
            Powered by <span className="font-medium text-blue-600">FormBooker</span>
          </p>
        </div>
      </div>
    </div>
  );
}
