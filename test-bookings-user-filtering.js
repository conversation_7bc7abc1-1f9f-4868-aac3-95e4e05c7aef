/**
 * Test script to verify user-based filtering for bookings
 * Tests that different user roles see appropriate bookings
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';

// Test users with different roles
const testUsers = {
  admin: {
    email: '<EMAIL>',
    password: 'password123',
    expectedRole: 'admin'
  },
  staff: {
    email: '<EMAIL>', 
    password: 'password123',
    expectedRole: 'staff'
  },
  customer: {
    email: '<EMAIL>',
    password: 'password123', 
    expectedRole: 'customer'
  }
};

async function loginUser(credentials) {
  try {
    const response = await axios.post(`${API_BASE_URL}/auth/login`, {
      email: credentials.email,
      password: credentials.password
    });
    
    return {
      success: true,
      token: response.data.data.tokens.accessToken,
      user: response.data.data.user
    };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.message || error.message
    };
  }
}

async function getBookings(token, params = {}) {
  try {
    const response = await axios.get(`${API_BASE_URL}/bookings`, {
      headers: {
        'Authorization': `Bearer ${token}`
      },
      params
    });
    
    return {
      success: true,
      data: response.data.data,
      pagination: response.data.pagination
    };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.message || error.message,
      status: error.response?.status
    };
  }
}

async function testUserBasedFiltering() {
  console.log('🧪 Testing User-Based Filtering for Bookings');
  console.log('==============================================');

  const results = {};

  for (const [roleType, credentials] of Object.entries(testUsers)) {
    console.log(`\n👤 Testing ${roleType.toUpperCase()} role`);
    console.log(`   Email: ${credentials.email}`);

    // Step 1: Login
    const loginResult = await loginUser(credentials);
    
    if (!loginResult.success) {
      console.log(`   ❌ Login failed: ${loginResult.error}`);
      results[roleType] = { loginSuccess: false, error: loginResult.error };
      continue;
    }

    console.log(`   ✅ Login successful`);
    console.log(`   📋 User role: ${loginResult.user.role}`);
    console.log(`   🔑 Token: ${loginResult.token.substring(0, 20)}...`);

    // Step 2: Get bookings
    const bookingsResult = await getBookings(loginResult.token);
    
    if (!bookingsResult.success) {
      console.log(`   ❌ Get bookings failed: ${bookingsResult.error}`);
      results[roleType] = { 
        loginSuccess: true, 
        bookingsSuccess: false, 
        error: bookingsResult.error,
        status: bookingsResult.status
      };
      continue;
    }

    console.log(`   ✅ Bookings retrieved successfully`);
    console.log(`   📊 Total bookings: ${bookingsResult.data.length}`);
    console.log(`   📄 Pagination: Page ${bookingsResult.pagination?.page || 1} of ${bookingsResult.pagination?.totalPages || 1}`);

    // Step 3: Analyze bookings data
    const bookings = bookingsResult.data;
    const analysis = {
      totalCount: bookings.length,
      statuses: {},
      branches: new Set(),
      customers: new Set(),
      services: new Set()
    };

    bookings.forEach(booking => {
      // Count statuses
      const status = booking.status || 'unknown';
      analysis.statuses[status] = (analysis.statuses[status] || 0) + 1;
      
      // Collect unique branches
      if (booking.branch?.name) {
        analysis.branches.add(booking.branch.name);
      }
      
      // Collect unique customers
      if (booking.customer?.user?.email) {
        analysis.customers.add(booking.customer.user.email);
      }
      
      // Collect unique services
      if (booking.service?.name) {
        analysis.services.add(booking.service.name);
      }
    });

    console.log(`   📈 Analysis:`);
    console.log(`      - Statuses: ${JSON.stringify(analysis.statuses)}`);
    console.log(`      - Unique branches: ${analysis.branches.size}`);
    console.log(`      - Unique customers: ${analysis.customers.size}`);
    console.log(`      - Unique services: ${analysis.services.size}`);

    // Step 4: Test filtering
    console.log(`   🔍 Testing filters...`);
    
    // Test status filter
    const pendingBookings = await getBookings(loginResult.token, { status: 'pending' });
    if (pendingBookings.success) {
      console.log(`      - Pending bookings: ${pendingBookings.data.length}`);
    }
    
    // Test date filter (today)
    const today = new Date().toISOString().split('T')[0];
    const todayBookings = await getBookings(loginResult.token, { bookingDate: today });
    if (todayBookings.success) {
      console.log(`      - Today's bookings: ${todayBookings.data.length}`);
    }

    results[roleType] = {
      loginSuccess: true,
      bookingsSuccess: true,
      userRole: loginResult.user.role,
      analysis,
      filterTests: {
        pending: pendingBookings.success ? pendingBookings.data.length : 'failed',
        today: todayBookings.success ? todayBookings.data.length : 'failed'
      }
    };
  }

  // Summary
  console.log('\n📊 Test Summary');
  console.log('================');
  
  let passedTests = 0;
  let totalTests = Object.keys(testUsers).length;
  
  for (const [roleType, result] of Object.entries(results)) {
    console.log(`\n${roleType.toUpperCase()}:`);
    
    if (result.loginSuccess && result.bookingsSuccess) {
      console.log(`  ✅ All tests passed`);
      console.log(`  📋 Role: ${result.userRole}`);
      console.log(`  📊 Bookings: ${result.analysis.totalCount}`);
      console.log(`  🏢 Branches: ${result.analysis.branches.size}`);
      console.log(`  👥 Customers: ${result.analysis.customers.size}`);
      passedTests++;
    } else {
      console.log(`  ❌ Tests failed: ${result.error}`);
    }
  }
  
  console.log(`\n🎯 Results: ${passedTests}/${totalTests} role tests passed`);
  console.log(`📈 Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);

  // Verification notes
  console.log('\n📝 Expected Behavior:');
  console.log('- Admin: Should see all bookings from all branches');
  console.log('- Staff: Should see only bookings from branches they manage');
  console.log('- Customer: Should see only their own bookings');
  
  return passedTests === totalTests;
}

// Run the test
if (require.main === module) {
  testUserBasedFiltering()
    .then(success => {
      console.log(success ? '\n🎉 All user-based filtering tests passed!' : '\n⚠️ Some tests failed.');
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('\n❌ Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { testUserBasedFiltering };
