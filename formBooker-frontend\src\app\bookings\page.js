'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardLayout from '@/components/layout/DashboardLayout';
import BookingsService from '@/services/bookingsService';
import {
  Calendar,
  Phone,
  Mail,
  Eye,
  MessageCircle,
  Filter,
  ChevronDown,
  Clock,
  MapPin,
  User,
  X,
  RefreshCw,
  AlertCircle
} from 'lucide-react';

/**
 * Bookings Management Page
 * Implements user-based filtering and real API integration
 */
export default function BookingsPage() {
  const { user, isAuthenticated } = useAuth();
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [statusFilter, setStatusFilter] = useState('all');
  const [timeFilter, setTimeFilter] = useState('all');

  // API data states
  const [bookings, setBookings] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  });

  // Notification state
  const [notification, setNotification] = useState(null);

  const showNotification = (message, type = 'info') => {
    setNotification({ message, type });
    setTimeout(() => setNotification(null), 5000);
  };

  // Load bookings from API
  const loadBookings = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const params = {
        page: pagination.page,
        limit: pagination.limit,
        sort: 'createdAt,desc'
      };

      // Add filters if selected
      if (statusFilter && statusFilter !== 'all') {
        params.status = statusFilter;
      }

      // Add time filter logic
      if (timeFilter && timeFilter !== 'all') {
        const now = new Date();
        switch (timeFilter) {
          case 'today':
            params.bookingDate = now.toISOString().split('T')[0];
            break;
          case 'this_week':
            const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
            const endOfWeek = new Date(now.setDate(now.getDate() - now.getDay() + 6));
            params.startDate = startOfWeek.toISOString().split('T')[0];
            params.endDate = endOfWeek.toISOString().split('T')[0];
            break;
          case 'this_month':
            const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
            const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
            params.startDate = startOfMonth.toISOString().split('T')[0];
            params.endDate = endOfMonth.toISOString().split('T')[0];
            break;
        }
      }

      console.log('🔍 Loading bookings with params:', params);
      console.log('👤 Current user:', { id: user?.id, role: user?.role });

      const response = await BookingsService.getBookings(params);

      console.log('📋 Bookings loaded:', response.data.length);

      setBookings(response.data || []);
      setPagination(response.pagination || pagination);

    } catch (error) {
      console.error('Failed to load bookings:', error);
      setError(error.message || 'Failed to load bookings. Please try again.');
      showNotification(error.message || 'Failed to load bookings. Please try again.', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // Load bookings on component mount and when filters change
  useEffect(() => {
    if (isAuthenticated && user) {
      loadBookings();
    }
  }, [isAuthenticated, user, statusFilter, timeFilter, pagination.page]);

  const statusOptions = [
    { value: 'all', label: 'All Status' },
    { value: 'pending', label: 'Pending' },
    { value: 'confirmed', label: 'Confirmed' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'completed', label: 'Completed' },
    { value: 'cancelled', label: 'Cancelled' },
    { value: 'no_show', label: 'No Show' }
  ];

  const timeOptions = [
    { value: 'all', label: 'All Time' },
    { value: 'today', label: 'Today' },
    { value: 'this_week', label: 'This Week' },
    { value: 'this_month', label: 'This Month' }
  ];

  const getStatusBadge = (status) => {
    switch (status?.toLowerCase()) {
      case 'confirmed':
        return 'badge-success';
      case 'pending':
        return 'badge-warning';
      case 'in_progress':
        return 'badge-info';
      case 'completed':
        return 'badge-primary';
      case 'cancelled':
      case 'no_show':
        return 'badge-error';
      default:
        return 'badge-neutral';
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  const formatTime = (timeString) => {
    if (!timeString) return 'N/A';
    try {
      const [hours, minutes] = timeString.split(':');
      const date = new Date();
      date.setHours(parseInt(hours), parseInt(minutes));
      return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });
    } catch {
      return timeString;
    }
  };

  const handleStatusChange = async (bookingId, newStatus) => {
    try {
      let response;

      switch (newStatus.toLowerCase()) {
        case 'confirmed':
          response = await BookingsService.confirmBooking(bookingId);
          break;
        case 'completed':
          response = await BookingsService.completeBooking(bookingId);
          break;
        case 'cancelled':
          const reason = prompt('Please enter cancellation reason:');
          if (!reason) return;
          response = await BookingsService.cancelBooking(bookingId, reason);
          break;
        default:
          response = await BookingsService.updateBooking(bookingId, { status: newStatus });
      }

      if (response.success) {
        showNotification(`Booking ${newStatus.toLowerCase()} successfully`, 'success');
        loadBookings(); // Reload bookings
      }
    } catch (error) {
      console.error('Failed to update booking status:', error);
      showNotification(error.message || 'Failed to update booking status', 'error');
    }
  };

  const handleContact = (type, contact) => {
    if (type === 'email') {
      window.open(`mailto:${contact}`);
    } else if (type === 'phone') {
      window.open(`tel:${contact}`);
    }
  };

  const handleRefresh = () => {
    loadBookings();
  };

  // Show loading state
  if (!isAuthenticated) {
    return (
      <DashboardLayout>
        <div className="py-6">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center py-12">
              <AlertCircle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-text mb-2">Authentication Required</h3>
              <p className="text-neutral-600">Please log in to view bookings.</p>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Notification */}
          {notification && (
            <div className={`mb-4 p-4 rounded-lg ${
              notification.type === 'error' ? 'bg-red-50 text-red-700 border border-red-200' :
              notification.type === 'success' ? 'bg-green-50 text-green-700 border border-green-200' :
              'bg-blue-50 text-blue-700 border border-blue-200'
            }`}>
              {notification.message}
            </div>
          )}

          {/* Header */}
          <div className="mb-8 flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-text">
                Bookings
                {user?.role === 'admin' && ' (All)'}
                {user?.role === 'staff' && ' (My Branches)'}
                {user?.role === 'customer' && ' (My Bookings)'}
              </h1>
              <p className="mt-2 text-neutral-600">
                {user?.role === 'admin' && 'Manage all customer appointments and bookings'}
                {user?.role === 'staff' && 'Manage bookings for your assigned branches'}
                {user?.role === 'customer' && 'View and manage your appointments'}
              </p>
            </div>
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="btn-outline btn-sm"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>

          {/* Filters */}
          <div className="mb-6 flex flex-col sm:flex-row gap-4 items-start sm:items-center">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-neutral-500" />
              <span className="text-sm font-medium text-neutral-700">Filter:</span>
            </div>

            {/* Status Filter */}
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="form-input w-auto"
              disabled={isLoading}
            >
              {statusOptions.map(option => (
                <option key={option.value} value={option.value}>{option.label}</option>
              ))}
            </select>

            {/* Time Filter */}
            <select
              value={timeFilter}
              onChange={(e) => setTimeFilter(e.target.value)}
              className="form-input w-auto"
              disabled={isLoading}
            >
              {timeOptions.map(option => (
                <option key={option.value} value={option.value}>{option.label}</option>
              ))}
            </select>

            {/* Results count */}
            <div className="text-sm text-neutral-600">
              {isLoading ? 'Loading...' : `${bookings.length} of ${pagination.total} bookings`}
            </div>
          </div>

          {/* Loading State */}
          {isLoading && (
            <div className="text-center py-12">
              <div className="w-16 h-16 border-4 border-gray-200 border-t-primary-500 rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-neutral-600">Loading bookings...</p>
            </div>
          )}

          {/* Error State */}
          {error && !isLoading && (
            <div className="text-center py-12">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-text mb-2">Error Loading Bookings</h3>
              <p className="text-neutral-600 mb-4">{error}</p>
              <button onClick={handleRefresh} className="btn-primary">
                Try Again
              </button>
            </div>
          )}

          {/* Bookings List */}
          {!isLoading && !error && (
            <div className="space-y-4">
              {bookings.length > 0 ? (
                bookings.map((booking) => (
                  <div key={booking.id} className="card">
                    <div className="card-body">
                      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        {/* Booking Info */}
                        <div className="flex-1">
                          <div className="flex items-start justify-between mb-3">
                            <div>
                              <h3 className="text-lg font-semibold text-text">
                                {booking.customer?.user?.name || 'Unknown Customer'} - {booking.service?.name || 'Unknown Service'}
                              </h3>
                              <div className="flex items-center space-x-4 text-sm text-neutral-600 mt-1">
                                <div className="flex items-center">
                                  <Calendar className="h-4 w-4 mr-1" />
                                  📅 {formatDate(booking.bookingDate)} at {formatTime(booking.startTime)}
                                </div>
                                <div className="flex items-center">
                                  <MapPin className="h-4 w-4 mr-1" />
                                  {booking.branch?.name || 'Unknown Branch'}
                                </div>
                              </div>
                              {booking.bookingCode && (
                                <div className="text-xs text-neutral-500 mt-1">
                                  Booking Code: {booking.bookingCode}
                                </div>
                              )}
                            </div>
                            <span className={`badge ${getStatusBadge(booking.status)}`}>
                              {booking.status?.charAt(0).toUpperCase() + booking.status?.slice(1) || 'Unknown'}
                            </span>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div className="flex items-center text-neutral-600">
                              <Phone className="h-4 w-4 mr-2" />
                              📞 {booking.customer?.user?.phone || 'N/A'}
                            </div>
                            <div className="flex items-center text-neutral-600">
                              <Mail className="h-4 w-4 mr-2" />
                              📧 {booking.customer?.user?.email || 'N/A'}
                            </div>
                          </div>

                          {(booking.customerNotes || booking.notes) && (
                            <div className="mt-3 p-3 bg-neutral-50 rounded-lg">
                              <p className="text-sm text-neutral-700">
                                <span className="font-medium">Notes:</span> {booking.customerNotes || booking.notes}
                              </p>
                            </div>
                          )}
                        </div>

                        {/* Actions */}
                        <div className="mt-4 lg:mt-0 lg:ml-6 flex flex-col sm:flex-row gap-2">
                          {/* Status Dropdown - Only for admin and staff */}
                          {(user?.role === 'admin' || user?.role === 'staff') && (
                            <select
                              value={booking.status || 'pending'}
                              onChange={(e) => handleStatusChange(booking.id, e.target.value)}
                              className="form-input text-sm"
                              disabled={isLoading}
                            >
                              <option value="pending">Pending</option>
                              <option value="confirmed">Confirmed</option>
                              <option value="in_progress">In Progress</option>
                              <option value="completed">Completed</option>
                              <option value="cancelled">Cancelled</option>
                              <option value="no_show">No Show</option>
                            </select>
                          )}

                          <div className="flex space-x-2">
                            <button
                              onClick={() => setSelectedBooking(booking)}
                              className="btn-outline btn-sm"
                            >
                              <Eye className="h-4 w-4 mr-1" />
                              View Details
                            </button>
                            <button
                              onClick={() => handleContact('phone', booking.customer?.user?.phone)}
                              className="btn-outline btn-sm"
                              disabled={!booking.customer?.user?.phone}
                            >
                              <Phone className="h-4 w-4 mr-1" />
                              Contact
                            </button>
                          </div>
                        </div>
                    </div>
                  </div>
                </div>
              ))
              ) : (
                /* Empty State */
                <div className="text-center py-12">
                  <div className="w-24 h-24 bg-neutral-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Calendar className="h-12 w-12 text-neutral-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-text mb-2">
                    No bookings found
                  </h3>
                  <p className="text-neutral-600 mb-6 max-w-md mx-auto">
                    {statusFilter !== 'all' || timeFilter !== 'all'
                      ? 'Try adjusting your filters to see more bookings.'
                      : user?.role === 'customer'
                        ? 'You haven\'t made any bookings yet.'
                        : 'When customers book through your forms, their appointments will appear here.'
                    }
                  </p>
                  {(statusFilter !== 'all' || timeFilter !== 'all') && (
                    <button
                      onClick={() => {
                        setStatusFilter('all');
                        setTimeFilter('all');
                      }}
                      className="btn-outline"
                    >
                      Clear Filters
                    </button>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Booking Details Modal */}
          {selectedBooking && (
            <div className="modal-overlay flex items-center justify-center p-4">
              <div className="modal-content animate-scale-in">
                <div className="flex items-center justify-between p-6 border-b border-neutral-200">
                  <h2 className="text-xl font-semibold text-text">Booking Details</h2>
                  <button
                    onClick={() => setSelectedBooking(null)}
                    className="p-1 hover:bg-neutral-100 rounded-lg transition-colors duration-200"
                  >
                    <X className="h-5 w-5 text-neutral-500" />
                  </button>
                </div>

                <div className="p-6 space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-neutral-700">Customer:</span>
                      <p className="text-text">{selectedBooking.customer?.user?.name || 'Unknown Customer'}</p>
                    </div>
                    <div>
                      <span className="font-medium text-neutral-700">Service:</span>
                      <p className="text-text">{selectedBooking.service?.name || 'Unknown Service'}</p>
                    </div>
                    <div>
                      <span className="font-medium text-neutral-700">Branch:</span>
                      <p className="text-text">{selectedBooking.branch?.name || 'Unknown Branch'}</p>
                    </div>
                    <div>
                      <span className="font-medium text-neutral-700">Date:</span>
                      <p className="text-text">{formatDate(selectedBooking.bookingDate)}</p>
                    </div>
                    <div>
                      <span className="font-medium text-neutral-700">Time:</span>
                      <p className="text-text">{formatTime(selectedBooking.startTime)} - {formatTime(selectedBooking.endTime)}</p>
                    </div>
                    <div>
                      <span className="font-medium text-neutral-700">Phone:</span>
                      <p className="text-text">{selectedBooking.customer?.user?.phone || 'N/A'}</p>
                    </div>
                    <div className="col-span-2">
                      <span className="font-medium text-neutral-700">Email:</span>
                      <p className="text-text">{selectedBooking.customer?.user?.email || 'N/A'}</p>
                    </div>
                    <div>
                      <span className="font-medium text-neutral-700">Booking Code:</span>
                      <p className="text-text font-mono">{selectedBooking.bookingCode || 'N/A'}</p>
                    </div>
                    <div>
                      <span className="font-medium text-neutral-700">Duration:</span>
                      <p className="text-text">{selectedBooking.duration || selectedBooking.service?.duration || 'N/A'} minutes</p>
                    </div>
                  </div>

                  {(selectedBooking.customerNotes || selectedBooking.notes) && (
                    <div>
                      <span className="font-medium text-neutral-700">Notes:</span>
                      <p className="text-text mt-1 p-3 bg-neutral-50 rounded-lg">
                        "{selectedBooking.customerNotes || selectedBooking.notes}"
                      </p>
                    </div>
                  )}

                  {(user?.role === 'admin' || user?.role === 'staff') && (
                    <div>
                      <span className="font-medium text-neutral-700">Status:</span>
                      <select
                        value={selectedBooking.status || 'pending'}
                        onChange={(e) => handleStatusChange(selectedBooking.id, e.target.value)}
                        className="form-input mt-1"
                        disabled={isLoading}
                      >
                        <option value="pending">Pending</option>
                        <option value="confirmed">Confirmed</option>
                        <option value="in_progress">In Progress</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                        <option value="no_show">No Show</option>
                      </select>
                    </div>
                  )}
                </div>

                <div className="flex space-x-3 p-6 border-t border-neutral-200">
                  <button
                    onClick={() => handleContact('email', selectedBooking.customer?.user?.email)}
                    className="btn-outline flex-1"
                    disabled={!selectedBooking.customer?.user?.email}
                  >
                    <Mail className="h-4 w-4 mr-2" />
                    Send Email
                  </button>
                  <button
                    onClick={() => handleContact('phone', selectedBooking.customer?.user?.phone)}
                    className="btn-outline flex-1"
                    disabled={!selectedBooking.customer?.user?.phone}
                  >
                    <Phone className="h-4 w-4 mr-2" />
                    Call Customer
                  </button>
                </div>

                <div className="flex space-x-3 px-6 pb-6">
                  <button className="btn-primary flex-1">
                    Edit Booking
                  </button>
                  <button className="btn-outline flex-1 text-red-600 border-red-300 hover:bg-red-50">
                    Cancel Booking
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
