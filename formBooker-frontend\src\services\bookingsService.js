import api from '../lib/api';

/**
 * Bookings Service
 * Handles all API calls related to bookings management
 */
class BookingsService {
  /**
   * Get all bookings with pagination and filters
   * Supports user-based filtering on backend based on role
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number
   * @param {number} params.limit - Items per page
   * @param {string} params.status - Filter by status
   * @param {number} params.customerId - Filter by customer ID
   * @param {number} params.serviceId - Filter by service ID
   * @param {number} params.branchId - Filter by branch ID
   * @param {number} params.employeeId - Filter by employee ID
   * @param {string} params.bookingDate - Filter by specific date
   * @param {string} params.startDate - Filter by date range start
   * @param {string} params.endDate - Filter by date range end
   * @param {string} params.paymentStatus - Filter by payment status
   * @param {string} params.search - Search term
   * @param {string} params.sort - Sort order
   * @returns {Promise<Object>} Bookings response
   */
  static async getBookings(params = {}) {
    try {
      console.log('🔍 BookingsService.getBookings - Debug');
      console.log('   Request params:', params);

      const endpoint = '/bookings';
      const requestParams = { ...params };

      const response = await api.get(endpoint, { params: requestParams });

      console.log('   Response structure:', Object.keys(response.data));
      console.log('   Bookings count:', response.data.data?.length || 0);

      return {
        success: true,
        data: response.data.data || [],
        pagination: response.data.pagination,
        message: response.data.message || 'Bookings retrieved successfully',
      };
    } catch (error) {
      console.error('Get bookings error:', error);

      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.error?.message ||
                          'Failed to fetch bookings. Please try again.';

      const validationErrors = error.response?.data?.errors || [];

      throw {
        success: false,
        message: errorMessage,
        errors: validationErrors,
        status: error.response?.status,
        code: error.response?.data?.error?.code,
      };
    }
  }

  /**
   * Get booking by ID
   * @param {number} id - Booking ID
   * @returns {Promise<Object>} Booking response
   */
  static async getBookingById(id) {
    try {
      const response = await api.get(`/bookings/${id}`);
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Booking retrieved successfully',
      };
    } catch (error) {
      console.error('Get booking by ID error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          'Failed to fetch booking details. Please try again.';
      
      throw {
        success: false,
        message: errorMessage,
        status: error.response?.status,
      };
    }
  }

  /**
   * Get booking by booking code
   * @param {string} code - Booking code
   * @returns {Promise<Object>} Booking response
   */
  static async getBookingByCode(code) {
    try {
      const response = await api.get(`/bookings/code/${code}`);
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Booking retrieved successfully',
      };
    } catch (error) {
      console.error('Get booking by code error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          'Failed to fetch booking details. Please try again.';
      
      throw {
        success: false,
        message: errorMessage,
        status: error.response?.status,
      };
    }
  }

  /**
   * Create a new booking
   * @param {Object} bookingData - Booking data
   * @returns {Promise<Object>} Create response
   */
  static async createBooking(bookingData) {
    try {
      const response = await api.post('/bookings', bookingData);
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Booking created successfully',
      };
    } catch (error) {
      console.error('Create booking error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          'Failed to create booking. Please try again.';
      
      const validationErrors = error.response?.data?.errors || [];
      
      throw {
        success: false,
        message: errorMessage,
        errors: validationErrors,
        status: error.response?.status,
      };
    }
  }

  /**
   * Update booking
   * @param {number} id - Booking ID
   * @param {Object} bookingData - Updated booking data
   * @returns {Promise<Object>} Update response
   */
  static async updateBooking(id, bookingData) {
    try {
      const response = await api.put(`/bookings/${id}`, bookingData);
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Booking updated successfully',
      };
    } catch (error) {
      console.error('Update booking error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          'Failed to update booking. Please try again.';
      
      throw {
        success: false,
        message: errorMessage,
        status: error.response?.status,
      };
    }
  }

  /**
   * Cancel booking
   * @param {number} id - Booking ID
   * @param {string} reason - Cancellation reason
   * @returns {Promise<Object>} Cancel response
   */
  static async cancelBooking(id, reason) {
    try {
      const response = await api.patch(`/bookings/${id}/cancel`, { reason });
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Booking cancelled successfully',
      };
    } catch (error) {
      console.error('Cancel booking error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          'Failed to cancel booking. Please try again.';
      
      throw {
        success: false,
        message: errorMessage,
        status: error.response?.status,
      };
    }
  }

  /**
   * Confirm booking
   * @param {number} id - Booking ID
   * @returns {Promise<Object>} Confirm response
   */
  static async confirmBooking(id) {
    try {
      const response = await api.patch(`/bookings/${id}/confirm`);
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Booking confirmed successfully',
      };
    } catch (error) {
      console.error('Confirm booking error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          'Failed to confirm booking. Please try again.';
      
      throw {
        success: false,
        message: errorMessage,
        status: error.response?.status,
      };
    }
  }

  /**
   * Complete booking
   * @param {number} id - Booking ID
   * @param {Object} completionData - Completion data (rating, review, etc.)
   * @returns {Promise<Object>} Complete response
   */
  static async completeBooking(id, completionData = {}) {
    try {
      const response = await api.patch(`/bookings/${id}/complete`, completionData);
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Booking completed successfully',
      };
    } catch (error) {
      console.error('Complete booking error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          'Failed to complete booking. Please try again.';
      
      throw {
        success: false,
        message: errorMessage,
        status: error.response?.status,
      };
    }
  }

  /**
   * Delete booking
   * @param {number} id - Booking ID
   * @returns {Promise<Object>} Delete response
   */
  static async deleteBooking(id) {
    try {
      const response = await api.delete(`/bookings/${id}`);
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Booking deleted successfully',
      };
    } catch (error) {
      console.error('Delete booking error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          'Failed to delete booking. Please try again.';
      
      throw {
        success: false,
        message: errorMessage,
        status: error.response?.status,
      };
    }
  }

  /**
   * Get booking statistics
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Statistics response
   */
  static async getBookingStats(params = {}) {
    try {
      const response = await api.get('/bookings/stats', { params });
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Statistics retrieved successfully',
      };
    } catch (error) {
      console.error('Get booking stats error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          'Failed to fetch booking statistics. Please try again.';
      
      throw {
        success: false,
        message: errorMessage,
        status: error.response?.status,
      };
    }
  }

  /**
   * Get available time slots
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Available slots response
   */
  static async getAvailableSlots(params) {
    try {
      const response = await api.get('/bookings/available-slots', { params });
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Available slots retrieved successfully',
      };
    } catch (error) {
      console.error('Get available slots error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          'Failed to fetch available slots. Please try again.';
      
      throw {
        success: false,
        message: errorMessage,
        status: error.response?.status,
      };
    }
  }
}

export default BookingsService;
