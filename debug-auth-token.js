/**
 * Debug Authentication Token
 * Tests token validity and API access
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';

// Test credentials
const testCredentials = {
  email: '<EMAIL>',
  password: 'admin123456'
};

async function debugAuthToken() {
  console.log('🔍 Debug Authentication Token');
  console.log('==============================');

  try {
    // Step 1: Login to get token
    console.log('\n1️⃣ Testing Login...');
    console.log(`   Email: ${testCredentials.email}`);
    
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, testCredentials);
    
    console.log('   ✅ Login successful');
    console.log('   Response structure:', Object.keys(loginResponse.data));
    console.log('   Full response data:', JSON.stringify(loginResponse.data, null, 2));

    const loginData = loginResponse.data.data || loginResponse.data;
    console.log('   Login data structure:', Object.keys(loginData));
    console.log('   Login data content:', JSON.stringify(loginData, null, 2));

    const token = loginData.tokens?.accessToken || loginData.accessToken || loginData.token;
    const user = loginData.user;
    
    console.log('   User info:', {
      id: user?.id,
      email: user?.email,
      role: user?.role,
      name: user?.name
    });
    console.log('   Token preview:', token ? `${token.substring(0, 30)}...` : 'No token');
    
    if (!token) {
      console.log('   ❌ No access token received');
      return false;
    }

    // Step 2: Decode token to check payload
    console.log('\n2️⃣ Analyzing Token...');
    try {
      const tokenParts = token.split('.');
      if (tokenParts.length !== 3) {
        console.log('   ❌ Invalid JWT format');
        return false;
      }
      
      const payload = JSON.parse(Buffer.from(tokenParts[1], 'base64').toString());
      const currentTime = Math.floor(Date.now() / 1000);
      
      console.log('   Token payload:', {
        id: payload.id,
        email: payload.email,
        role: payload.role,
        iat: payload.iat ? new Date(payload.iat * 1000).toISOString() : 'N/A',
        exp: payload.exp ? new Date(payload.exp * 1000).toISOString() : 'N/A',
        timeUntilExpiry: payload.exp ? `${payload.exp - currentTime} seconds` : 'N/A',
        isExpired: payload.exp ? payload.exp < currentTime : 'Unknown'
      });
    } catch (error) {
      console.log('   ⚠️ Could not decode token:', error.message);
    }

    // Step 3: Test API endpoints with token
    console.log('\n3️⃣ Testing API Endpoints...');
    
    const endpoints = [
      { name: 'Profile', url: '/auth/profile', method: 'GET' },
      { name: 'Bookings', url: '/bookings', method: 'GET' },
      { name: 'Services', url: '/services', method: 'GET' },
      { name: 'Branches', url: '/branches', method: 'GET' }
    ];
    
    for (const endpoint of endpoints) {
      try {
        console.log(`\n   Testing ${endpoint.name} (${endpoint.method} ${endpoint.url})...`);
        
        const config = {
          method: endpoint.method,
          url: `${API_BASE_URL}${endpoint.url}`,
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          timeout: 5000
        };
        
        const response = await axios(config);
        
        console.log(`   ✅ ${endpoint.name}: ${response.status} ${response.statusText}`);
        console.log(`      Data count: ${Array.isArray(response.data.data) ? response.data.data.length : 'N/A'}`);
        
      } catch (error) {
        console.log(`   ❌ ${endpoint.name}: ${error.response?.status || 'Network Error'} ${error.response?.statusText || error.message}`);
        if (error.response?.data) {
          console.log(`      Error details:`, error.response.data);
        }
      }
    }

    // Step 4: Test token without Bearer prefix
    console.log('\n4️⃣ Testing Token Format Issues...');
    try {
      const response = await axios.get(`${API_BASE_URL}/auth/profile`, {
        headers: {
          'Authorization': token, // Without "Bearer " prefix
          'Content-Type': 'application/json'
        }
      });
      console.log('   ⚠️ Token works without Bearer prefix (unexpected)');
    } catch (error) {
      console.log('   ✅ Token correctly requires Bearer prefix');
    }

    // Step 5: Test with invalid token
    console.log('\n5️⃣ Testing Invalid Token...');
    try {
      const response = await axios.get(`${API_BASE_URL}/auth/profile`, {
        headers: {
          'Authorization': 'Bearer invalid_token_here',
          'Content-Type': 'application/json'
        }
      });
      console.log('   ⚠️ Invalid token was accepted (security issue)');
    } catch (error) {
      console.log('   ✅ Invalid token correctly rejected:', error.response?.status);
    }

    return true;

  } catch (error) {
    console.error('\n❌ Debug failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    }
    return false;
  }
}

// Test with different user roles
async function testMultipleRoles() {
  console.log('\n🎭 Testing Multiple User Roles');
  console.log('===============================');

  const testUsers = [
    { email: '<EMAIL>', password: 'admin123456', expectedRole: 'admin' }
    // Note: Only testing with admin user for now since we don't have other test users
  ];

  for (const credentials of testUsers) {
    console.log(`\n👤 Testing ${credentials.expectedRole.toUpperCase()} role...`);
    
    try {
      // Login
      const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, credentials);
      const token = loginResponse.data.data?.token;
      const user = loginResponse.data.data?.user;
      
      console.log(`   ✅ Login successful: ${user?.email} (${user?.role})`);
      
      // Test bookings endpoint
      try {
        const bookingsResponse = await axios.get(`${API_BASE_URL}/bookings`, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        console.log(`   ✅ Bookings access: ${bookingsResponse.status} (${bookingsResponse.data.data?.length || 0} bookings)`);
      } catch (error) {
        console.log(`   ❌ Bookings access: ${error.response?.status} ${error.response?.data?.message || error.message}`);
      }
      
    } catch (error) {
      console.log(`   ❌ Login failed: ${error.response?.data?.message || error.message}`);
    }
  }
}

// Run tests
if (require.main === module) {
  (async () => {
    const success = await debugAuthToken();
    await testMultipleRoles();
    
    console.log('\n📊 Debug Summary');
    console.log('=================');
    console.log(success ? '✅ Basic authentication working' : '❌ Authentication issues detected');
    console.log('Check the detailed logs above for specific issues.');
  })();
}

module.exports = { debugAuthToken, testMultipleRoles };
